import { useMutation } from '@tanstack/react-query';
import axios from 'axios';
import { setAuthToken } from '../../../lib/cookies';

const login = async (data: { email: string; password: string }) => {
  // Convert data to URL-encoded format
  const formData = new URLSearchParams();
  formData.append('email', data.email);
  formData.append('password', data.password);

  const response = await axios.post(
    'https://api-dev.tikram-group.com/api/v1.0/company/login',
    formData,
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'language': 'ar'
      }
    }
  );

  return response.data;
};

export function useLogin() {
  const mutation = useMutation({
    mutationFn: login,
    onSuccess: (data) => {
      // Store token in cookies instead of localStorage
      setAuthToken(data.data.token);
    },
    onError: (error) => {
      console.error('Login failed:', error);
    }
  });

  return mutation;
}
