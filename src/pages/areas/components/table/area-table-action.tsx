import PopupModal from '@/components/shared/popup-modal';
import TableSearchInput from '@/components/shared/table-search-input';
import CountryCreateForm from '../forms/area-form';
import { usePermissions } from '@/contexts/PermissionsContext';

export default function AreaTableActions() {
  const { hasPermission } = usePermissions();
  const canCreate = hasPermission('areas', 'create');

  return (
    <div className="flex items-center justify-between gap-2 py-5">
      <div className="flex flex-1 gap-4">
        <TableSearchInput placeholder="Search Areas Here" />
      </div>
      <div className="flex gap-3">
        {canCreate && (
          <PopupModal
            renderModal={(onClose) => (
              <CountryCreateForm modalClose={onClose} />
            )}
          />
        )}
      </div>
    </div>
  );
}
