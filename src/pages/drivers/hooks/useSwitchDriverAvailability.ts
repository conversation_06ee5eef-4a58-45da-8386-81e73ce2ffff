import { useMutation, useQueryClient } from '@tanstack/react-query';
import { client, setHeaderToken, refreshAuth } from '../../../lib/axiosClient';
import { redirect } from 'react-router-dom';
import toast from 'react-hot-toast';

const switchDriverAvailability = async (driverId: number) => {
  const token = localStorage.getItem('token');

  try {
    // This endpoint toggles the driver's availability status:
    // If is_available = true (available) → becomes false (unavailable)
    // If is_available = false (unavailable) → becomes true (available)
    const response = await client.put(
      `/admin/drivers/${driverId}/switch-availability`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response?.status === 401) {
      const newToken = await refreshAuth();
      if (newToken) {
        setHeaderToken(newToken);
        const retryResponse = await client.put(
          `/admin/drivers/${driverId}/switch-availability`,
          {},
          {
            headers: {
              Authorization: `Bear<PERSON> ${newToken}`
            }
          }
        );
        return retryResponse.data;
      } else {
        localStorage.removeItem('token');
        toast.error('Authentication failed');
        redirect('/login');
        throw new Error('Authentication failed');
      }
    } else {
      throw error;
    }
  }
};

export function useSwitchDriverAvailability() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: switchDriverAvailability,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['drivers'] });
    },
    onError: (error) => {
      console.error('Switch driver availability error:', error);
    }
  });
}
