import { useMutation, useQueryClient } from '@tanstack/react-query';
import { client, setHeaderToken, refreshAuth } from '../../../lib/axiosClient';
import { redirect } from 'react-router-dom';
import toast from 'react-hot-toast';

const lockUnlockDriver = async (driverId: number) => {
  const token = localStorage.getItem('token');

  try {
    // This endpoint toggles the driver's lock status:
    // If is_application_locked = 1 (locked) → becomes 0 (unlocked)
    // If is_application_locked = 0 (unlocked) → becomes 1 (locked)
    const response = await client.put(
      `/admin/drivers/${driverId}/switch-lock`,
      {},
      {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );
    return response.data;
  } catch (error: any) {
    if (error.response?.status === 401) {
      const newToken = await refreshAuth();
      if (newToken) {
        setHeaderToken(newToken);
        const retryResponse = await client.put(
          `/admin/drivers/${driverId}/switch-lock`,
          {},
          {
            headers: {
              Authorization: `Bear<PERSON> ${newToken}`
            }
          }
        );
        return retryResponse.data;
      } else {
        localStorage.removeItem('token');
        toast.error('Authentication failed');
        redirect('/login');
        throw new Error('Authentication failed');
      }
    } else {
      throw error;
    }
  }
};

export function useLockUnlockDriver() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: lockUnlockDriver,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['drivers'] });
    },
    onError: (error) => {
      console.error('Lock/Unlock driver error:', error);
    }
  });
}
