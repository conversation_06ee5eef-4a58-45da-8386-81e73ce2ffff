import { useQuery } from '@tanstack/react-query';
import { client, setHeaderToken, refreshAuth } from '../../../lib/axiosClient';
import { redirect } from 'react-router-dom';
import toast from 'react-hot-toast';

// Driver Details Response interface
export interface DriverDetailsResponse {
  status: boolean;
  message: string;
  data: {
    id: number;
    country_id: number;
    city_id: number;
    area_id: number;
    first_name: string;
    last_name: string;
    date_of_birth: string;
    phone_number: string;
    email: string;
    nationality: string;
    national_id: string;
    vehicle_type: 'CAR' | 'MOTORCYCLE' | 'BICYCLE';
    plate_number: string;
    has_driving_license: boolean;
    has_worked_before: boolean;
    notes: string;
    profile_image: {
      path: string;
      url: string;
    };
    vehicle_image: {
      path: string;
      url: string;
    };
    max_capacity: number;
    vehicle_max_distance: number;
    status: 'ACTIVE' | 'INACTIVE';
    is_available: boolean;
    starting_work_at: string;
    finishing_work_at: string;
    is_application_locked: boolean;
  };
}

const fetchDriverDetails = async (
  driverId: number
): Promise<DriverDetailsResponse> => {
  const token = localStorage.getItem('token');

  try {
    const response = await client.get(`/admin/drivers/${driverId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    return response.data;
  } catch (error: any) {
    if (error.response?.status === 401) {
      const newToken = await refreshAuth();
      if (newToken) {
        setHeaderToken(newToken);
        const retryResponse = await client.get(`/admin/drivers/${driverId}`, {
          headers: {
            Authorization: `Bearer ${newToken}`
          }
        });
        return retryResponse.data;
      } else {
        localStorage.removeItem('token');
        toast.error('Authentication failed');
        redirect('/login');
        throw new Error('Authentication failed');
      }
    } else {
      throw error;
    }
  }
};

export function useFetchDriverDetails(driverId: number) {
  return useQuery({
    queryKey: ['driver-details', driverId],
    queryFn: () => fetchDriverDetails(driverId),
    enabled: !!driverId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1
  });
}
