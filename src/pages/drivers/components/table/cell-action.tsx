import { AlertModal } from '@/components/shared/alert-modal';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  MoreHorizontal,
  Trash,
  Lock,
  Unlock,
  Eye,
  Edit,
  Power,
  PowerOff,
  ToggleLeft,
  ToggleRight
} from 'lucide-react';
import { useState } from 'react';
import { Driver } from '../../lib/types';
import DriverForm from '../forms/driver-form';
import DriverDetailsModal from '../forms/driver-details-modal';
import { useDeleteDriver } from '../../hooks/useDeleteDriver';
import { useLockUnlockDriver } from '../../hooks/useLockUnlockDriver';
import { useSwitchDriverActivation } from '../../hooks/useSwitchDriverActivation';
import { useSwitchDriverAvailability } from '../../hooks/useSwitchDriverAvailability';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import toast from 'react-hot-toast';
import { usePermissions } from '@/contexts/PermissionsContext';

interface CellActionProps {
  data: Driver;
  driver: Driver;
}

export const CellAction: React.FC<CellActionProps> = ({ data }) => {
  const [open, setOpen] = useState(false);
  const [showLockAlert, setShowLockAlert] = useState(false);
  const [showActivationAlert, setShowActivationAlert] = useState(false);
  const [showAvailabilityAlert, setShowAvailabilityAlert] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const { hasPermission } = usePermissions();
  const canUpdate = hasPermission('drivers', 'update');
  const canDelete = hasPermission('drivers', 'delete');

  const { mutate: deleteDriver, isPending } = useDeleteDriver();
  const { mutate: lockUnlockDriver, isPending: isLockUnlocking } =
    useLockUnlockDriver();
  const { mutate: switchDriverActivation, isPending: isSwitchingActivation } =
    useSwitchDriverActivation();

  // Helper function to normalize boolean values (handles both number and boolean)
  const normalizeBooleanValue = (
    value: number | boolean | undefined
  ): boolean => {
    if (typeof value === 'boolean') return value;
    if (typeof value === 'number') return value === 1;
    return false;
  };
  const {
    mutate: switchDriverAvailability,
    isPending: isSwitchingAvailability
  } = useSwitchDriverAvailability();

  const onConfirm = async () => {
    deleteDriver(data.id, {
      onSuccess: () => {
        toast.success('driver deleted successfully');
        setOpen(false);
      },
      onError: (error) => {
        toast.error('something went wrong');
        console.log(error);
      }
    });
  };

  const handleLockUnlock = () => {
    // Check current lock status: 1 = locked, 0 = unlocked
    const isCurrentlyLocked = normalizeBooleanValue(data.is_application_locked);
    const action = isCurrentlyLocked ? 'unlocked' : 'locked';

    lockUnlockDriver(data.id, {
      onSuccess: () => {
        toast.success(`Driver ${action} successfully`);
        setShowLockAlert(false);
      },
      onError: () => {
        toast.error(
          `Failed to ${isCurrentlyLocked ? 'unlock' : 'lock'} driver`
        );
      }
    });
  };

  const handleSwitchActivation = () => {
    // Check current availability status: true = available, false = unavailable
    const isCurrentlyAvailable = normalizeBooleanValue(data.is_available);
    const action = isCurrentlyAvailable ? 'deactivated' : 'activated';

    switchDriverActivation(data.id, {
      onSuccess: () => {
        toast.success(`Driver ${action} successfully`);
        setShowActivationAlert(false);
      },
      onError: () => {
        toast.error(
          `Failed to ${isCurrentlyAvailable ? 'deactivate' : 'activate'} driver`
        );
      }
    });
  };

  const handleSwitchAvailability = () => {
    // Check current availability status: true = available, false = unavailable
    const isCurrentlyAvailable = normalizeBooleanValue(data.is_available);
    const action = isCurrentlyAvailable ? 'made unavailable' : 'made available';

    switchDriverAvailability(data.id, {
      onSuccess: () => {
        toast.success(`Driver ${action} successfully`);
        setShowAvailabilityAlert(false);
      },
      onError: () => {
        toast.error(
          `Failed to ${isCurrentlyAvailable ? 'make unavailable' : 'make available'} driver`
        );
      }
    });
  };

  return (
    <>
      <AlertModal
        isOpen={open}
        onClose={() => setOpen(false)}
        onConfirm={onConfirm}
        loading={isPending}
        title="Delete Driver"
        description="Are you sure you want to delete this driver? This action cannot be undone."
      />

      {/* Lock/Unlock Confirmation */}
      <AlertModal
        isOpen={showLockAlert}
        onClose={() => setShowLockAlert(false)}
        onConfirm={handleLockUnlock}
        loading={isLockUnlocking}
        title={
          normalizeBooleanValue(data.is_application_locked)
            ? 'Unlock Driver'
            : 'Lock Driver'
        }
        description={
          normalizeBooleanValue(data.is_application_locked)
            ? 'Are you sure you want to unlock this driver? This will allow the driver to access the application.'
            : 'Are you sure you want to lock this driver? This will prevent the driver from accessing the application.'
        }
      />

      {/* Switch Activation Confirmation */}
      <AlertModal
        isOpen={showActivationAlert}
        onClose={() => setShowActivationAlert(false)}
        onConfirm={handleSwitchActivation}
        loading={isSwitchingActivation}
        title={
          normalizeBooleanValue(data.is_available)
            ? 'Deactivate Driver'
            : 'Activate Driver'
        }
        description={
          normalizeBooleanValue(data.is_available)
            ? 'Are you sure you want to deactivate this driver? This will make the driver unavailable for new orders.'
            : 'Are you sure you want to activate this driver? This will make the driver available for new orders.'
        }
      />

      {/* Switch Availability Confirmation */}
      <AlertModal
        isOpen={showAvailabilityAlert}
        onClose={() => setShowAvailabilityAlert(false)}
        onConfirm={handleSwitchAvailability}
        loading={isSwitchingAvailability}
        title={
          normalizeBooleanValue(data.is_available)
            ? 'Make Driver Unavailable'
            : 'Make Driver Available'
        }
        description={
          normalizeBooleanValue(data.is_available)
            ? 'Are you sure you want to make this driver unavailable? This will toggle their availability status.'
            : 'Are you sure you want to make this driver available? This will toggle their availability status.'
        }
      />

      {/* View Details Modal */}
      <Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
        <DialogContent className="max-w-4xl p-0">
          <DriverDetailsModal
            modalClose={() => setShowDetailsModal(false)}
            driverId={data.id}
          />
        </DialogContent>
      </Dialog>

      {/* Update Driver Modal */}
      <Dialog open={showUpdateModal} onOpenChange={setShowUpdateModal}>
        <DialogContent className="max-w-2xl">
          <DriverForm
            modalClose={() => setShowUpdateModal(false)}
            driver={data}
          />
        </DialogContent>
      </Dialog>

      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>

          <div className="flex flex-col gap-2">
            {/* View Details */}
            <Button
              onClick={() => setShowDetailsModal(true)}
              className="w-full justify-start p-2"
              variant="ghost"
            >
              <Eye className="mr-2 h-4 w-4 text-gray-400" />
              View Details
            </Button>

            {/* Update Driver */}
            {canUpdate && (
              <Button
                onClick={() => setShowUpdateModal(true)}
                className="w-full justify-start p-2"
                variant="ghost"
              >
                <Edit className="mr-2 h-4 w-4 text-gray-400" />
                Update
              </Button>
            )}

            {/* Lock/Unlock Driver */}
            {canUpdate && (
              <Button
                onClick={() => setShowLockAlert(true)}
                className="w-full justify-start p-2"
                variant="ghost"
                disabled={isLockUnlocking}
              >
                {normalizeBooleanValue(data.is_application_locked) ? (
                  <>
                    <Unlock className="mr-2 h-4 w-4 text-green-600" />
                    Unlock
                  </>
                ) : (
                  <>
                    <Lock className="mr-2 h-4 w-4 text-orange-600" />
                    Lock
                  </>
                )}
              </Button>
            )}

            {/* Switch Activation Driver */}
            {canUpdate && (
              <Button
                onClick={() => setShowActivationAlert(true)}
                className="w-full justify-start p-2"
                variant="ghost"
                disabled={isSwitchingActivation}
              >
                {normalizeBooleanValue(data.is_available) ? (
                  <>
                    <PowerOff className="mr-2 h-4 w-4 text-red-600" />
                    Deactivate
                  </>
                ) : (
                  <>
                    <Power className="mr-2 h-4 w-4 text-green-600" />
                    Activate
                  </>
                )}
              </Button>
            )}

            {/* Switch Availability Driver */}
            {canUpdate && (
              <Button
                onClick={() => setShowAvailabilityAlert(true)}
                className="w-full justify-start p-2"
                variant="ghost"
                disabled={isSwitchingAvailability}
              >
                {normalizeBooleanValue(data.is_available) ? (
                  <>
                    <ToggleLeft className="mr-2 h-4 w-4 text-orange-600" />
                    Make Unavailable
                  </>
                ) : (
                  <>
                    <ToggleRight className="mr-2 h-4 w-4 text-blue-600" />
                    Make Available
                  </>
                )}
              </Button>
            )}

            {/* Delete Driver */}
            {canDelete && (
              <Button
                onClick={() => setOpen(true)}
                className="w-full justify-start p-2"
                variant="ghost"
              >
                <Trash className="mr-2 h-4 w-4 text-gray-400" />
                Delete
              </Button>
            )}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  );
};
