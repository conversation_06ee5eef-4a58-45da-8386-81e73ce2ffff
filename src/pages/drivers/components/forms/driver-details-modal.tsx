import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import {
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  Car,
  Clock,
  Shield,
  Loader2,
  X,
  FileText,
  Truck,
  Lock,
  Unlock
} from 'lucide-react';
import { useFetchDriverDetails } from '../../hooks/useFetchDriverDetails';

interface DriverDetailsModalProps {
  modalClose: () => void;
  driverId: number;
}

const DriverDetailsModal = ({
  modalClose,
  driverId
}: DriverDetailsModalProps) => {
  const { data, isLoading, error } = useFetchDriverDetails(driverId);

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  const getStatusColor = (status: string) => {
    return status === 'ACTIVE'
      ? 'bg-gray-700 text-green-400 border-gray-600'
      : 'bg-gray-700 text-red-400 border-gray-600';
  };

  const getVehicleIcon = (vehicleType: string) => {
    switch (vehicleType) {
      case 'CAR':
        return <Car className="h-4 w-4" />;
      case 'MOTORCYCLE':
        return <Truck className="h-4 w-4" />;
      case 'BICYCLE':
        return <Car className="h-4 w-4" />;
      default:
        return <Car className="h-4 w-4" />;
    }
  };

  const formatTime = (timeString: string) => {
    return new Date(`1970-01-01T${timeString}`).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: true
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center bg-[#020817] p-12 text-white">
        <div className="text-center">
          <Loader2 className="mx-auto mb-4 h-8 w-8 animate-spin text-gray-400" />
          <p className="text-gray-400">Loading driver details...</p>
        </div>
      </div>
    );
  }

  if (error || !data?.data) {
    return (
      <div className="flex items-center justify-center bg-[#020817] p-12 text-white">
        <div className="text-center">
          <X className="mx-auto mb-4 h-8 w-8 text-gray-400" />
          <p className="mb-4 text-gray-400">Failed to load driver details</p>
          <Button
            onClick={modalClose}
            variant="outline"
            className="border-gray-600 text-white hover:bg-gray-800"
          >
            Close
          </Button>
        </div>
      </div>
    );
  }

  const driver = data.data;

  return (
    <div className="mx-auto max-h-[80vh] max-w-4xl overflow-y-auto bg-[#020817] p-6 text-white">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between border-b border-gray-700 pb-4">
        <h2 className="text-xl font-bold text-white">Driver Details</h2>
        <Button
          variant="ghost"
          size="sm"
          onClick={modalClose}
          className="text-gray-400 hover:bg-gray-800 hover:text-white"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Driver Profile Section */}
      <div className="mb-6 grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Profile Info */}
        <div className="text-center">
          <Avatar className="mx-auto mb-4 h-24 w-24 border-2 border-gray-600">
            <AvatarImage
              src={driver.profile_image?.url}
              alt={`${driver.first_name} ${driver.last_name}`}
            />
            <AvatarFallback className="bg-gray-700 text-xl text-white">
              {getInitials(driver.first_name, driver.last_name)}
            </AvatarFallback>
          </Avatar>

          <h3 className="mb-2 text-lg font-semibold text-white">
            {driver.first_name} {driver.last_name}
          </h3>

          <div className="mb-2 flex justify-center gap-2">
            <Badge
              variant="outline"
              className={`${getStatusColor(driver.status)} font-medium`}
            >
              {driver.status}
            </Badge>
            <Badge
              variant="outline"
              className={`${driver.is_application_locked ? 'bg-gray-700 text-red-400' : 'bg-gray-700 text-green-400'} border-gray-600 font-medium`}
            >
              {driver.is_application_locked ? (
                <>
                  <Lock className="mr-1 h-3 w-3" />
                  Locked
                </>
              ) : (
                <>
                  <Unlock className="mr-1 h-3 w-3" />
                  Unlocked
                </>
              )}
            </Badge>
          </div>

          <Badge
            variant="outline"
            className={`${driver.is_available ? 'bg-gray-700 text-green-400' : 'bg-gray-700 text-gray-400'} border-gray-600`}
          >
            {driver.is_available ? 'Available' : 'Unavailable'}
          </Badge>
        </div>

        {/* Vehicle Image */}
        <div className="text-center">
          <div className="mx-auto mb-4 flex h-24 w-24 items-center justify-center rounded-lg border-2 border-gray-600 bg-gray-800">
            {driver.vehicle_image?.url ? (
              <img
                src={driver.vehicle_image.url}
                alt="Vehicle"
                className="h-full w-full rounded-lg object-cover"
              />
            ) : (
              <Car className="h-12 w-12 text-gray-400" />
            )}
          </div>
          <p className="text-sm text-gray-400">Vehicle</p>
          <p className="font-medium text-white">{driver.vehicle_type}</p>
        </div>

        {/* Quick Stats */}
        <div className="space-y-3">
          <div className="rounded-lg bg-gray-800 p-3 text-center">
            <p className="text-sm text-gray-400">Driver ID</p>
            <p className="font-bold text-white">#{driver.id}</p>
          </div>
          <div className="rounded-lg bg-gray-800 p-3 text-center">
            <p className="text-sm text-gray-400">Max Capacity</p>
            <p className="font-bold text-white">{driver.max_capacity}</p>
          </div>
          <div className="rounded-lg bg-gray-800 p-3 text-center">
            <p className="text-sm text-gray-400">Max Distance</p>
            <p className="font-bold text-white">
              {driver.vehicle_max_distance} km
            </p>
          </div>
        </div>
      </div>

      {/* Information Grid */}
      <div className="mb-6 grid grid-cols-1 gap-4 md:grid-cols-2">
        {/* Personal Information */}
        <div className="space-y-3">
          <h4 className="mb-3 border-b border-gray-700 pb-2 text-lg font-semibold text-white">
            Personal Information
          </h4>

          <div className="flex items-center space-x-3 rounded-lg bg-gray-800 p-3">
            <Mail className="h-5 w-5 text-gray-400" />
            <div>
              <p className="text-sm text-gray-400">Email</p>
              <p className="font-medium text-white">{driver.email}</p>
            </div>
          </div>

          <div className="flex items-center space-x-3 rounded-lg bg-gray-800 p-3">
            <Phone className="h-5 w-5 text-gray-400" />
            <div>
              <p className="text-sm text-gray-400">Phone</p>
              <p className="font-medium text-white">{driver.phone_number}</p>
            </div>
          </div>

          <div className="flex items-center space-x-3 rounded-lg bg-gray-800 p-3">
            <Calendar className="h-5 w-5 text-gray-400" />
            <div>
              <p className="text-sm text-gray-400">Date of Birth</p>
              <p className="font-medium text-white">
                {formatDate(driver.date_of_birth)}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3 rounded-lg bg-gray-800 p-3">
            <MapPin className="h-5 w-5 text-gray-400" />
            <div>
              <p className="text-sm text-gray-400">Nationality</p>
              <p className="font-medium text-white">{driver.nationality}</p>
            </div>
          </div>

          <div className="flex items-center space-x-3 rounded-lg bg-gray-800 p-3">
            <FileText className="h-5 w-5 text-gray-400" />
            <div>
              <p className="text-sm text-gray-400">National ID</p>
              <p className="font-medium text-white">{driver.national_id}</p>
            </div>
          </div>
        </div>

        {/* Vehicle & Work Information */}
        <div className="space-y-3">
          <h4 className="mb-3 border-b border-gray-700 pb-2 text-lg font-semibold text-white">
            Vehicle & Work Information
          </h4>

          <div className="flex items-center space-x-3 rounded-lg bg-gray-800 p-3">
            {getVehicleIcon(driver.vehicle_type)}
            <div>
              <p className="text-sm text-gray-400">Vehicle Type</p>
              <p className="font-medium text-white">{driver.vehicle_type}</p>
            </div>
          </div>

          <div className="flex items-center space-x-3 rounded-lg bg-gray-800 p-3">
            <FileText className="h-5 w-5 text-gray-400" />
            <div>
              <p className="text-sm text-gray-400">Plate Number</p>
              <p className="font-medium text-white">{driver.plate_number}</p>
            </div>
          </div>

          <div className="flex items-center space-x-3 rounded-lg bg-gray-800 p-3">
            <Clock className="h-5 w-5 text-gray-400" />
            <div>
              <p className="text-sm text-gray-400">Work Hours</p>
              <p className="font-medium text-white">
                {formatTime(driver.starting_work_at)} -{' '}
                {formatTime(driver.finishing_work_at)}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3 rounded-lg bg-gray-800 p-3">
            <Shield className="h-5 w-5 text-gray-400" />
            <div>
              <p className="text-sm text-gray-400">Driving License</p>
              <p className="font-medium text-white">
                {driver.has_driving_license ? 'Yes' : 'No'}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3 rounded-lg bg-gray-800 p-3">
            <User className="h-5 w-5 text-gray-400" />
            <div>
              <p className="text-sm text-gray-400">Previous Experience</p>
              <p className="font-medium text-white">
                {driver.has_worked_before ? 'Yes' : 'No'}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Notes Section */}
      {driver.notes && (
        <div className="mb-6">
          <h4 className="mb-3 border-b border-gray-700 pb-2 text-lg font-semibold text-white">
            Notes
          </h4>
          <div className="rounded-lg bg-gray-800 p-4">
            <p className="text-gray-300">{driver.notes}</p>
          </div>
        </div>
      )}

      {/* Close Button */}
      <div className="flex justify-center">
        <Button
          onClick={modalClose}
          className="w-full bg-gray-700 text-white hover:bg-gray-600"
        >
          Close
        </Button>
      </div>
    </div>
  );
};

export default DriverDetailsModal;
