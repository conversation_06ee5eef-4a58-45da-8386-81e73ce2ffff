import { Checkbox } from '@/components/ui/checkbox';
import { ColumnDef } from '@tanstack/react-table';
// import { CellAction } from './cell-action';
import { Restaurant } from '../../lib/types';
import { CellAction } from './cell-action';

export const columns: ColumnDef<Restaurant>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'logo',
    header: 'Logo',
    cell: ({ row }) => (
      <div className="flex items-center">
        {row.original.logo?.url ? (
          <img
            src={row.original.logo.url}
            alt={`${row.original.name_en} logo`}
            className="h-10 w-10 rounded-full object-cover"
          />
        ) : (
          <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200">
            <span className="text-xs text-gray-500">No Logo</span>
          </div>
        )}
      </div>
    )
  },
  {
    accessorKey: 'name_en',
    header: 'Name'
  },
  {
    accessorKey: 'email',
    header: 'Email'
  },
  {
    accessorKey: 'phone',
    header: 'Phone'
  },
  {
    accessorKey: 'address_en',
    header: 'Address'
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      const status = row.original.status;
      const statusColors = {
        PENDING: 'bg-yellow-100 text-yellow-800',
        APPROVED: 'bg-green-100 text-green-800',
        REJECTED: 'bg-red-100 text-red-800',
        SUSPENDED: 'bg-gray-100 text-gray-800'
      };

      return (
        <span
          className={`rounded-full px-2 py-1 text-xs font-medium ${statusColors[status]}`}
        >
          {status}
        </span>
      );
    }
  },
  {
    accessorKey: 'is_available',
    header: 'Available',
    cell: ({ row }) => (
      <span
        className={`rounded-full px-2 py-1 text-xs font-medium ${
          row.original.is_available
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}
      >
        {row.original.is_available ? 'Available' : 'Unavailable'}
      </span>
    )
  },
  {
    accessorKey: 'start_time',
    header: 'Hours',
    cell: ({ row }) => (
      <span className="text-sm">
        {row.original.start_time} - {row.original.end_time}
      </span>
    )
  },
  {
    id: 'actions',
    cell: ({ row }) => <CellAction data={row.original} />
  }
];
