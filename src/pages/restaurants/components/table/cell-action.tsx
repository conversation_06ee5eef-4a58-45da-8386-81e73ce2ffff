import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  MoreHorizontal,
  CheckCircle,
  XCircle,
  Ban,
  RotateCcw
} from 'lucide-react';
import { Restaurant } from '../../lib/types';
import PopupModal from '@/components/shared/popup-modal';
import RestaurantDetailsModal from '../forms/restaurant-details-modal';
import RestaurantRejectModal from '../forms/restaurant-reject-modal';

import {
  useApproveRestaurant,
  useSuspendRestaurant
} from '../../hooks/useRestaurantActions';
import toast from 'react-hot-toast';
import { useState } from 'react';
import { AlertModal } from '@/components/shared/alert-modal';
import { Dialog, DialogContent } from '@/components/ui/dialog';

interface CellActionProps {
  data: Restaurant;
}

export const CellAction: React.FC<CellActionProps> = ({ data }) => {
  const [showApproveAlert, setShowApproveAlert] = useState(false);
  const [showSuspendAlert, setShowSuspendAlert] = useState(false);
  const [showRejectModal, setShowRejectModal] = useState(false);

  const { mutate: approveRestaurant, isPending: isApproving } =
    useApproveRestaurant();
  const { mutate: suspendRestaurant, isPending: isSuspending } =
    useSuspendRestaurant();

  const handleApprove = () => {
    approveRestaurant(data.id, {
      onSuccess: () => {
        toast.success('Restaurant approved successfully');
        setShowApproveAlert(false);
      },
      onError: () => {
        toast.error('Failed to approve restaurant');
      }
    });
  };

  const handleSuspendToggle = () => {
    const isCurrentlySuspended = data.status === 'SUSPENDED_BY_ADMIN';
    const action = isCurrentlySuspended ? 'unsuspended' : 'suspended';

    suspendRestaurant(data.id, {
      onSuccess: () => {
        toast.success(`Restaurant ${action} successfully`);
        setShowSuspendAlert(false);
      },
      onError: () => {
        toast.error(
          `Failed to ${isCurrentlySuspended ? 'unsuspend' : 'suspend'} restaurant`
        );
      }
    });
  };

  const canApprove = data.status === 'PENDING';
  const canReject = data.status === 'PENDING';
  const canSuspend = data.status === 'APPROVED';
  const canUnsuspend = data.status === 'SUSPENDED_BY_ADMIN';

  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="min-w-[160px]">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <div className="flex flex-col gap-2">
            {/* View Details - Always visible for all restaurants */}
            <PopupModal
              title="View Details"
              icon="Eye"
              renderModal={(onClose) => (
                <RestaurantDetailsModal
                  modalClose={onClose}
                  restaurantId={data.id}
                />
              )}
            />

            {/* Approve Restaurant */}
            {canApprove && (
              <Button
                onClick={() => setShowApproveAlert(true)}
                className="w-full justify-start p-2"
                variant="ghost"
                disabled={isApproving}
              >
                <CheckCircle className="mr-2 h-4 w-4 text-green-600" />
                Approve
              </Button>
            )}

            {/* Reject Restaurant */}
            {canReject && (
              <Button
                onClick={() => setShowRejectModal(true)}
                className="w-full justify-start p-2"
                variant="ghost"
              >
                <XCircle className="mr-2 h-4 w-4 text-red-600" />
                Reject
              </Button>
            )}

            {/* Suspend Restaurant */}
            {canSuspend && (
              <Button
                onClick={() => setShowSuspendAlert(true)}
                className="w-full justify-start p-2"
                variant="ghost"
                disabled={isSuspending}
              >
                <Ban className="mr-2 h-4 w-4 text-orange-600" />
                Suspend
              </Button>
            )}

            {/* Unsuspend Restaurant */}
            {canUnsuspend && (
              <Button
                onClick={() => setShowSuspendAlert(true)}
                className="w-full justify-start p-2"
                variant="ghost"
                disabled={isSuspending}
              >
                <RotateCcw className="mr-2 h-4 w-4 text-green-600" />
                Unsuspend
              </Button>
            )}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Approve Confirmation */}
      <AlertModal
        isOpen={showApproveAlert}
        onClose={() => setShowApproveAlert(false)}
        onConfirm={handleApprove}
        loading={isApproving}
        title="Approve Restaurant"
        description="Are you sure you want to approve this restaurant? This action will make the restaurant visible to customers."
      />

      {/* Suspend/Unsuspend Confirmation */}
      <AlertModal
        isOpen={showSuspendAlert}
        onClose={() => setShowSuspendAlert(false)}
        onConfirm={handleSuspendToggle}
        loading={isSuspending}
        title={
          data.status === 'SUSPENDED_BY_ADMIN'
            ? 'Unsuspend Restaurant'
            : 'Suspend Restaurant'
        }
        description={
          data.status === 'SUSPENDED_BY_ADMIN'
            ? 'Are you sure you want to unsuspend this restaurant? This will make the restaurant available again.'
            : 'Are you sure you want to suspend this restaurant? This will temporarily disable the restaurant.'
        }
      />

      {/* Reject Restaurant Modal */}
      <Dialog open={showRejectModal} onOpenChange={setShowRejectModal}>
        <DialogContent className="max-w-2xl">
          <RestaurantRejectModal
            modalClose={() => setShowRejectModal(false)}
            restaurant={data}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};
