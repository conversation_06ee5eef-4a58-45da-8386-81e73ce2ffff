import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { XCircle, Loader2 } from 'lucide-react';
import { useState } from 'react';
import { Restaurant } from '../../lib/types';
import { useRejectRestaurant } from '../../hooks/useRestaurantActions';
import toast from 'react-hot-toast';

interface RestaurantRejectModalProps {
  modalClose: () => void;
  restaurant: Restaurant;
}

const RestaurantRejectModal = ({
  modalClose,
  restaurant
}: RestaurantRejectModalProps) => {
  const [rejectionReason, setRejectionReason] = useState('');
  const { mutate: rejectRestaurant, isPending: isRejecting } =
    useRejectRestaurant();

  const handleReject = () => {
    if (!rejectionReason.trim()) {
      toast.error('Please provide a rejection reason');
      return;
    }

    rejectRestaurant(
      { id: restaurant.id, rejection_reason: rejectionReason },
      {
        onSuccess: () => {
          toast.success('Restaurant rejected successfully');
          modalClose();
        },
        onError: () => {
          toast.error('Failed to reject restaurant');
        }
      }
    );
  };

  return (
    <div className="space-y-6 bg-[#020817] p-6 text-white">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-700 pb-4">
        <div className="flex items-center">
          <XCircle className="mr-3 h-6 w-6 text-red-500" />
          <div>
            <h2 className="text-xl font-bold text-white">Reject Restaurant</h2>
            <p className="text-sm text-gray-400">{restaurant.name_en}</p>
          </div>
        </div>
        <Button
          variant="outline"
          onClick={modalClose}
          disabled={isRejecting}
          className="border-gray-600 text-white hover:bg-gray-800"
        >
          Cancel
        </Button>
      </div>

      {/* Rejection Reason Form */}
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="rejection-reason" className="text-white">
            Rejection Reason <span className="text-red-400">*</span>
          </Label>
          <p className="text-sm text-gray-400">
            Please provide a reason for rejecting this restaurant.
          </p>
        </div>

        <Textarea
          id="rejection-reason"
          value={rejectionReason}
          onChange={(e) => setRejectionReason(e.target.value)}
          placeholder="Enter rejection reason..."
          className="min-h-[100px] resize-none border-gray-700 bg-gray-800 text-white placeholder:text-gray-500"
          disabled={isRejecting}
        />
      </div>

      {/* Action Buttons */}
      <div className="flex gap-3 border-t border-gray-700 pt-4">
        <Button
          variant="outline"
          onClick={modalClose}
          disabled={isRejecting}
          className="flex-1 border-gray-600 text-white hover:bg-gray-800"
        >
          Cancel
        </Button>
        <Button
          onClick={handleReject}
          disabled={isRejecting || !rejectionReason.trim()}
          className="flex-1 bg-red-600 text-white hover:bg-red-700"
        >
          {isRejecting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Rejecting...
            </>
          ) : (
            <>
              <XCircle className="mr-2 h-4 w-4" />
              Reject Restaurant
            </>
          )}
        </Button>
      </div>
    </div>
  );
};

export default RestaurantRejectModal;
