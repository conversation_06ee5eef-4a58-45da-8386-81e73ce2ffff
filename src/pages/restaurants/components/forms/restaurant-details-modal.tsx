import Heading from '@/components/shared/heading';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useFetchRestaurant } from '../../hooks/useFetchRestaurant';
import {
  Loader2,
  MapPin,
  Clock,
  Phone,
  Mail,
  Globe,
  Facebook,
  Instagram
} from 'lucide-react';

interface RestaurantDetailsModalProps {
  modalClose: () => void;
  restaurantId: number;
}

const RestaurantDetailsModal = ({
  modalClose,
  restaurantId
}: RestaurantDetailsModalProps) => {
  const { restaurant, isLoading } = useFetchRestaurant(restaurantId);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading restaurant details...</span>
      </div>
    );
  }

  // Restaurant not found state
  if (!restaurant) {
    return (
      <div className="p-6">
        <div className="text-center">
          <p>Restaurant not found</p>
          <Button onClick={modalClose} className="mt-4">
            Close
          </Button>
        </div>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'APPROVED':
        return 'bg-green-100 text-green-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      case 'SUSPENDED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="w-full max-w-none space-y-6 p-6">
      <div className="flex items-center justify-between">
        <Heading title={restaurant.name_en} description="Restaurant Details" />
        <Button variant="outline" onClick={modalClose}>
          Close
        </Button>
      </div>

      {/* Restaurant Logo */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Restaurant Logo</h3>
        <div className="flex justify-center">
          {restaurant.logo?.url ? (
            <img
              src={restaurant.logo.url}
              alt={`${restaurant.name_en} logo`}
              className="h-32 w-32 rounded-lg border object-cover shadow-sm"
            />
          ) : (
            <div className="flex h-32 w-32 items-center justify-center rounded-lg border bg-gray-200">
              <span className="text-gray-500">No Logo</span>
            </div>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Basic Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Basic Information</h3>
          <div className="space-y-3">
            <div className="flex justify-between">
              <span className="font-medium">Status:</span>
              <Badge className={getStatusColor(restaurant.status)}>
                {restaurant.status}
              </Badge>
            </div>
            <div className="flex justify-between">
              <span className="font-medium">Available:</span>
              <Badge
                className={
                  restaurant.is_available
                    ? 'bg-green-100 text-green-800'
                    : 'bg-red-100 text-red-800'
                }
              >
                {restaurant.is_available ? 'Available' : 'Unavailable'}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className="text-sm">
                {restaurant.start_time} - {restaurant.end_time}
              </span>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Contact Information</h3>
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Mail className="h-4 w-4 text-gray-500" />
              <span className="text-sm">{restaurant.email}</span>
            </div>
            <div className="flex items-center gap-2">
              <Phone className="h-4 w-4 text-gray-500" />
              <span className="text-sm">{restaurant.phone}</span>
            </div>
            {restaurant.contact_number && (
              <div className="flex items-center gap-2">
                <Phone className="h-4 w-4 text-gray-500" />
                <span className="text-sm">{restaurant.contact_number}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Names in Different Languages */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Restaurant Names</h3>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <span className="text-sm font-medium text-gray-700">English:</span>
            <p className="text-sm">{restaurant.name_en}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-700">Arabic:</span>
            <p className="text-sm">{restaurant.name_ar}</p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-700">Turkish:</span>
            <p className="text-sm">{restaurant.name_tr}</p>
          </div>
        </div>
      </div>

      {/* Addresses */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Addresses</h3>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <span className="text-sm font-medium text-gray-700">English:</span>
            <p className="flex items-start gap-2 text-sm">
              <MapPin className="mt-0.5 h-4 w-4 text-gray-500" />
              {restaurant.address_en}
            </p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-700">Arabic:</span>
            <p className="flex items-start gap-2 text-sm">
              <MapPin className="mt-0.5 h-4 w-4 text-gray-500" />
              {restaurant.address_ar}
            </p>
          </div>
          <div>
            <span className="text-sm font-medium text-gray-700">Turkish:</span>
            <p className="flex items-start gap-2 text-sm">
              <MapPin className="mt-0.5 h-4 w-4 text-gray-500" />
              {restaurant.address_tr}
            </p>
          </div>
        </div>
      </div>

      {/* Location Coordinates */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Location</h3>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <div className="flex justify-between">
            <span className="font-medium">Latitude:</span>
            <span>{restaurant.latitude}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">Longitude:</span>
            <span>{restaurant.longitude}</span>
          </div>
        </div>
      </div>

      {/* Social Media & Website */}
      {(restaurant.website_url ||
        restaurant.facebook_url ||
        restaurant.instagram_url) && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Online Presence</h3>
          <div className="flex flex-wrap gap-4">
            {restaurant.website_url && (
              <a
                href={restaurant.website_url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-blue-600 hover:text-blue-800"
              >
                <Globe className="h-4 w-4" />
                Website
              </a>
            )}
            {restaurant.facebook_url && (
              <a
                href={restaurant.facebook_url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-blue-600 hover:text-blue-800"
              >
                <Facebook className="h-4 w-4" />
                Facebook
              </a>
            )}
            {restaurant.instagram_url && (
              <a
                href={restaurant.instagram_url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2 text-pink-600 hover:text-pink-800"
              >
                <Instagram className="h-4 w-4" />
                Instagram
              </a>
            )}
          </div>
        </div>
      )}

      {/* Rejection Reason (if rejected) */}
      {restaurant.status === 'REJECTED' && restaurant.rejection_reason && (
        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-red-600">
            Rejection Reason
          </h3>
          <div className="rounded-lg border border-red-200 bg-red-50 p-4">
            <p className="text-sm text-red-800">
              {restaurant.rejection_reason}
            </p>
          </div>
        </div>
      )}

      {/* Timestamps */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold">Timestamps</h3>
        <div className="grid grid-cols-1 gap-4 text-sm md:grid-cols-2">
          <div className="flex justify-between">
            <span className="font-medium">Created:</span>
            <span>{new Date(restaurant.created_at).toLocaleString()}</span>
          </div>
          <div className="flex justify-between">
            <span className="font-medium">Updated:</span>
            <span>{new Date(restaurant.updated_at).toLocaleString()}</span>
          </div>
          {restaurant.approved_at && (
            <div className="flex justify-between">
              <span className="font-medium">Approved:</span>
              <span>{new Date(restaurant.approved_at).toLocaleString()}</span>
            </div>
          )}
          {restaurant.rejected_at && (
            <div className="flex justify-between">
              <span className="font-medium">Rejected:</span>
              <span>{new Date(restaurant.rejected_at).toLocaleString()}</span>
            </div>
          )}
          {restaurant.suspended_at && (
            <div className="flex justify-between">
              <span className="font-medium">Suspended:</span>
              <span>{new Date(restaurant.suspended_at).toLocaleString()}</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default RestaurantDetailsModal;
