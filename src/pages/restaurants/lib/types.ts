export interface Restaurant {
  id: number;
  name_en: string;
  name_ar: string;
  name_tr: string;
  email: string;
  phone: string;
  contact_number: string;
  address_en: string;
  address_ar: string;
  address_tr: string;
  latitude: number;
  longitude: number;
  start_time: string;
  end_time: string;
  is_available: boolean;
  facebook_url?: string;
  instagram_url?: string;
  website_url?: string;
  status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'SUSPENDED_BY_ADMIN';
  rejection_reason?: string;
  logo?: {
    id: number;
    url: string;
    path: string;
  };
  approved_at?: string;
  rejected_at?: string;
  suspended_at?: string;
  created_at: string;
  updated_at: string;
}

// API Response interfaces
export interface RestaurantDetailsResponse {
  status: boolean;
  message: string;
  data: Restaurant;
}

export interface RestaurantsListResponse {
  status: boolean;
  message: string;
  data: {
    items: Restaurant[];
    pagination: {
      current_page: number;
      last_page: number;
      from: number;
      to: number;
      per_page: number;
      total: number;
    };
  };
}
