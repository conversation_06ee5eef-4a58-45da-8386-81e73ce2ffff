import PopupModal from '@/components/shared/popup-modal';
import TableSearchInput from '@/components/shared/table-search-input';
import ManagerForm from '../forms/manager-form';

export default function ManagerTableActions() {
  return (
    <div className="flex items-center justify-between gap-2 py-5">
      <div className="flex flex-1 gap-4">
        <TableSearchInput placeholder="Search Managers Here" />
      </div>
      <div className="flex gap-3">
        <PopupModal
          renderModal={(onClose) => <ManagerForm modalClose={onClose} />}
        />
      </div>
    </div>
  );
}
