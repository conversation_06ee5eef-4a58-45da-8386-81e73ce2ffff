import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, Ban, Eye } from 'lucide-react';
import { Manager } from '../../lib/types';
import PopupModal from '@/components/shared/popup-modal';
import ManagerForm from '../forms/manager-form';
import ManagerDetailsModal from '../forms/manager-details-modal';
import { useUpdateManager } from '../../hooks/useUpdateManager';
import { AlertModal } from '@/components/shared/alert-modal';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import toast from 'react-hot-toast';
import { useState } from 'react';

interface CellActionProps {
  data: Manager;
  manager: Manager;
}

export const CellAction: React.FC<CellActionProps> = ({ data }) => {
  const [showSuspendAlert, setShowSuspendAlert] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const { mutate: updateManager, isPending: isUpdating } = useUpdateManager();

  const handleSuspend = () => {
    const updateData = {
      username: data.username,
      email: data.email,
      status: 'INACTIVE',
      profile_image: data.profile_image.path
    };

    updateManager(
      { id: data.id, data: updateData },
      {
        onSuccess: () => {
          toast.success('Manager suspended successfully');
          setShowSuspendAlert(false);
        },
        onError: () => {
          toast.error('Failed to suspend manager');
        }
      }
    );
  };

  const canSuspend = data.status === 'ACTIVE';

  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>
          <div className="flex flex-col gap-2">
            {/* View Details */}
            <Button
              onClick={() => setShowDetailsModal(true)}
              className="w-full justify-start p-2"
              variant="ghost"
            >
              <Eye className="mr-2 h-4 w-4 text-blue-600" />
              View Details
            </Button>

            <PopupModal
              title="Update Manager"
              icon="Edit"
              renderModal={(onClose) => (
                <ManagerForm modalClose={onClose} manager={data} />
              )}
            />

            {/* Suspend Manager */}
            {canSuspend && (
              <Button
                onClick={() => setShowSuspendAlert(true)}
                className="w-full justify-start p-2"
                variant="ghost"
                disabled={isUpdating}
              >
                <Ban className="mr-2 h-4 w-4 text-orange-600" />
                Suspend
              </Button>
            )}
          </div>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Suspend Confirmation */}
      <AlertModal
        isOpen={showSuspendAlert}
        onClose={() => setShowSuspendAlert(false)}
        onConfirm={handleSuspend}
        loading={isUpdating}
        title="Suspend Manager"
        description="Are you sure you want to suspend this manager? This will change their status to INACTIVE."
      />

      {/* View Details Modal */}
      <Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
        <DialogContent className="max-w-md p-0">
          <ManagerDetailsModal
            modalClose={() => setShowDetailsModal(false)}
            managerId={data.id}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};
