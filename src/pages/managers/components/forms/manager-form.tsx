import Heading from '@/components/shared/heading';
import { But<PERSON> } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';

import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useUpdateManager } from '../../hooks/useUpdateManager'; // Hook for updating city
import { useCreateManager } from '../../hooks/useCreateCity';
import toast from 'react-hot-toast';
import { Manager } from '../../lib/types';
import { Label } from '@/components/ui/label';
import { useUploadImage } from '@/pages/drivers/hooks/useUploadImage';
import { useState } from 'react';
import { X } from 'lucide-react';

// Create two separate schemas for create and update
const createManagerSchema = z
  .object({
    username: z
      .string({ required_error: 'User name is required' })
      .min(1, { message: 'User name should be at least 1 character' }),
    email: z
      .string({ required_error: 'Manager Email is required' })
      .min(1, { message: 'Manager Email should be at least 1 character' }),
    password: z
      .string({ required_error: 'Password is required' })
      .min(6, { message: 'Password must be at least 6 characters' }),
    password_confirmation: z.string({
      required_error: 'Password confirmation is required'
    }),
    status: z.boolean().default(true), // Default to ACTIVE
    profile_image: z.string().optional()
  })
  .refine((data) => data.password === data.password_confirmation, {
    message: "Passwords don't match",
    path: ['password_confirmation']
  });

const updateManagerSchema = z.object({
  username: z
    .string({ required_error: 'User name is required' })
    .min(1, { message: 'User name should be at least 1 character' }),
  email: z
    .string({ required_error: 'Manager Email is required' })
    .min(1, { message: 'Manager Email should be at least 1 character' }),
  status: z.boolean().default(true), // Default to ACTIVE
  profile_image: z.string().optional()
});

// Use a type that can represent both schemas
type CreateManagerFormValues = z.infer<typeof createManagerSchema>;
// type UpdateManagerFormValues = z.infer<typeof updateManagerSchema>;
type ManagerFormValues = CreateManagerFormValues;

interface ManagerFormProps {
  modalClose: () => void;
  manager?: Manager;
}

const ManagerForm = ({ modalClose, manager }: ManagerFormProps) => {
  // Use different schema based on whether we're creating or updating
  const form = useForm<ManagerFormValues>({
    resolver: zodResolver(manager ? updateManagerSchema : createManagerSchema),
    defaultValues: manager
      ? {
          username: manager?.username,
          email: manager?.email,
          password: '',
          password_confirmation: '',
          status: manager?.status === 'ACTIVE', // Convert string to boolean
          profile_image: manager.profile_image.path
        }
      : {
          username: '',
          email: '',
          password: '',
          password_confirmation: '',
          status: true,
          profile_image: ''
        } // Default to active
  });
  const { mutate: uploadImage, isPending: isUploading } = useUploadImage();
  const [previewImages, setPreviewImages] = useState<Record<string, string>>(
    manager?.profile_image?.url
      ? { MANAGER_PROFILE: manager.profile_image.url }
      : {}
  );
  const handleImageChange = (type: 'MANAGER_PROFILE', file: File) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    const previewUrl = URL.createObjectURL(file);
    setPreviewImages({ [type]: previewUrl });

    uploadImage(formData, {
      onSuccess: (data) => {
        form.setValue('profile_image', data.data.path);
      },
      onError: (error) => {
        console.error(`Error uploading ${type} image:`, error);
        setPreviewImages({ [type]: '' });
      }
    });
  };

  const handleRemoveImage = (type: string) => {
    // Revoke the object URL to avoid memory leaks
    if (previewImages[type]) {
      URL.revokeObjectURL(previewImages[type]);
    }
    setPreviewImages((prev) => ({ ...prev, [type]: '' }));
    form.setValue('profile_image', '');
  };
  const { mutate: createManager, isPending: isCreating } = useCreateManager();
  const { mutate: updateManager, isPending: isUpdating } = useUpdateManager();
  // const { countries, isLoading } = useFetchCountries(1);

  const onSubmit = async (data: ManagerFormValues) => {
    // Convert boolean status to string
    const formattedData = {
      ...data,
      status: data.status ? 'ACTIVE' : 'INACTIVE'
    };

    if (manager) {
      // If manager exists, update it with only the fields needed for update
      const updateData = {
        username: data.username,
        email: data.email,
        profile_image: data.profile_image || manager.profile_image.path // Use existing path if no new image
      };

      updateManager(
        {
          id: manager.id,
          data: updateData
        },
        {
          onSuccess: () => {
            toast.success('Manager updated successfully');
            modalClose();
          },
          onError: (error: any) => {
            if (error.message && error.message.includes('Validation failed')) {
              toast.error('Validation failed. Please check your inputs.');
            } else {
              toast.error('Failed to update manager');
            }
            console.log(error);
          }
        }
      );
    } else {
      // Otherwise, create a new manager with all fields
      createManager(formattedData, {
        onSuccess: () => {
          toast.success('New manager added successfully');
          modalClose();
        },
        onError: (error: any) => {
          if (error.message && error.message.includes('Validation failed')) {
            toast.error('Validation failed. Please check your inputs.');
          } else {
            toast.error('Failed to create manager');
          }
          console.log(error);
        }
      });
    }
  };

  return (
    <div className="p-5">
      <Heading
        title={manager ? 'Update Manager' : 'Create New Manager'}
        description={
          manager
            ? 'Update username, email, and profile image'
            : 'Create a new manager with all details'
        }
        className="space-y-2 py-4 text-center"
      />
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-4"
          autoComplete="off"
        >
          {/* City Name Field */}
          <FormField
            control={form.control}
            name="username"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="flex flex-col gap-2">
                    <Label>User Name</Label>
                    <Input
                      placeholder="User Name"
                      {...field}
                      className="px-4 py-6 shadow-inner drop-shadow-xl"
                      disabled={isCreating || isUpdating}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <div className="flex flex-col gap-2">
                    <Label>Email</Label>
                    <Input
                      placeholder="Email address"
                      type="email"
                      {...field}
                      className="px-4 py-6 shadow-inner drop-shadow-xl"
                      disabled={isCreating || isUpdating}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Password Fields - Only show when creating a new manager */}
          {!manager && (
            <>
              {/* Password Field */}
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="flex flex-col gap-2">
                        <Label>Password</Label>
                        <Input
                          placeholder="Enter password"
                          type="password"
                          {...field}
                          className="px-4 py-6 shadow-inner drop-shadow-xl"
                          disabled={isCreating || isUpdating}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Password Confirmation Field */}
              <FormField
                control={form.control}
                name="password_confirmation"
                render={({ field }) => (
                  <FormItem>
                    <FormControl>
                      <div className="flex flex-col gap-2">
                        <Label>Confirm Password</Label>
                        <Input
                          placeholder="Confirm password"
                          type="password"
                          {...field}
                          className="px-4 py-6 shadow-inner drop-shadow-xl"
                          disabled={isCreating || isUpdating}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}

          {/* Status Checkbox - Only show when creating a new manager */}
          {!manager && (
            <FormField
              control={form.control}
              name="status"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <Label className="text-sm font-medium">Active Status</Label>
                    <p className="text-sm text-muted-foreground">
                      Check this box to set the manager status as ACTIVE
                    </p>
                  </div>
                </FormItem>
              )}
            />
          )}

          {/* Profile Image */}
          <div className="flex flex-col items-center gap-4">
            <Label htmlFor="profile_image">Profile Image:</Label>

            {previewImages.MANAGER_PROFILE ? (
              <div className="relative">
                <img
                  src={previewImages.MANAGER_PROFILE}
                  alt="Profile preview"
                  className="h-32 w-32 rounded-lg border-2 border-gray-200 bg-gray-50 object-contain"
                />
                <button
                  type="button"
                  onClick={() => handleRemoveImage('MANAGER_PROFILE')}
                  className="absolute -right-2 -top-2 rounded-full bg-red-500 p-1 text-white shadow-sm hover:bg-red-600"
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
            ) : (
              <>
                <div className="flex h-32 w-32 items-center justify-center rounded-lg border-2 border-dashed border-gray-300 bg-gray-50">
                  <span className="text-sm text-gray-500">No Image</span>
                </div>

                <FormField
                  name="profile_image"
                  render={({}) => (
                    <FormItem>
                      <FormControl>
                        <Input
                          className="h-10 max-w-xs"
                          id="profile_image"
                          type="file"
                          accept="image/*"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) {
                              handleImageChange('MANAGER_PROFILE', file);
                              e.target.value = '';
                            }
                          }}
                          disabled={isUploading}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </>
            )}
          </div>

          {/* Buttons */}
          <div className="flex items-center justify-center gap-4">
            <Button
              type="button"
              variant="secondary"
              className="rounded-full"
              size="lg"
              onClick={modalClose}
              disabled={isCreating || isUpdating}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="rounded-full"
              size="lg"
              disabled={isCreating || isUpdating}
            >
              {manager ? 'Update Manager' : 'Create Manager'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default ManagerForm;
