import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { User, Mail, Shield, Loader2, X } from 'lucide-react';
import { useFetchManagerDetails } from '../../hooks/useFetchManagerDetails';

interface ManagerDetailsModalProps {
  modalClose: () => void;
  managerId: number;
}

const ManagerDetailsModal = ({
  modalClose,
  managerId
}: ManagerDetailsModalProps) => {
  const { data, isLoading, error } = useFetchManagerDetails(managerId);

  const getInitials = (username: string) => {
    return username.charAt(0).toUpperCase();
  };

  const getStatusColor = (status: string) => {
    return status === 'ACTIVE'
      ? 'bg-gray-700 text-green-400 border-gray-600'
      : 'bg-gray-700 text-red-400 border-gray-600';
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center bg-[#020817] p-12 text-white">
        <div className="text-center">
          <Loader2 className="mx-auto mb-4 h-8 w-8 animate-spin text-gray-400" />
          <p className="text-gray-400">Loading manager details...</p>
        </div>
      </div>
    );
  }

  if (error || !data?.data) {
    return (
      <div className="flex items-center justify-center bg-[#020817] p-12 text-white">
        <div className="text-center">
          <X className="mx-auto mb-4 h-8 w-8 text-gray-400" />
          <p className="mb-4 text-gray-400">Failed to load manager details</p>
          <Button
            onClick={modalClose}
            variant="outline"
            className="border-gray-600 text-white hover:bg-gray-800"
          >
            Close
          </Button>
        </div>
      </div>
    );
  }

  const manager = data.data;

  return (
    <div className="mx-auto max-w-md bg-[#020817] p-6 text-white">
      {/* Header */}
      <div className="mb-6 flex items-center justify-between border-b border-gray-700 pb-4">
        <h2 className="text-xl font-bold text-white">Manager Details</h2>
        <Button
          variant="ghost"
          size="sm"
          onClick={modalClose}
          className="text-gray-400 hover:bg-gray-800 hover:text-white"
        >
          <X className="h-4 w-4" />
        </Button>
      </div>

      {/* Manager Profile */}
      <div className="mb-6 text-center">
        <Avatar className="mx-auto mb-4 h-20 w-20 border-2 border-gray-600">
          <AvatarImage
            src={manager.profile_image?.url}
            alt={manager.username}
          />
          <AvatarFallback className="bg-gray-700 text-xl text-white">
            {getInitials(manager.username)}
          </AvatarFallback>
        </Avatar>

        <h3 className="mb-2 text-lg font-semibold text-white">
          {manager.username}
        </h3>

        <Badge
          variant="outline"
          className={`${getStatusColor(manager.status)} font-medium`}
        >
          {manager.status}
        </Badge>
      </div>

      {/* Manager Information */}
      <div className="mb-6 space-y-4">
        <div className="flex items-center space-x-3 rounded-lg bg-gray-800 p-3">
          <User className="h-5 w-5 text-gray-400" />
          <div>
            <p className="text-sm text-gray-400">Username</p>
            <p className="font-medium text-white">{manager.username}</p>
          </div>
        </div>

        <div className="flex items-center space-x-3 rounded-lg bg-gray-800 p-3">
          <Mail className="h-5 w-5 text-gray-400" />
          <div>
            <p className="text-sm text-gray-400">Email</p>
            <p className="font-medium text-white">{manager.email}</p>
          </div>
        </div>

        <div className="flex items-center space-x-3 rounded-lg bg-gray-800 p-3">
          <Shield className="h-5 w-5 text-gray-400" />
          <div>
            <p className="text-sm text-gray-400">Status</p>
            <p className="font-medium text-white">{manager.status}</p>
          </div>
        </div>

        <div className="flex items-center space-x-3 rounded-lg bg-gray-800 p-3">
          <User className="h-5 w-5 text-gray-400" />
          <div>
            <p className="text-sm text-gray-400">Manager ID</p>
            <p className="font-medium text-white">#{manager.id}</p>
          </div>
        </div>
      </div>

      {/* Close Button */}
      <div className="flex justify-center">
        <Button
          onClick={modalClose}
          className="w-full bg-gray-700 text-white hover:bg-gray-600"
        >
          Close
        </Button>
      </div>
    </div>
  );
};

export default ManagerDetailsModal;
