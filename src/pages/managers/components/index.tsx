import DataTable from '@/components/shared/data-table';
import { columns } from './table/columns';
import { useEffect, useState } from 'react';
import { useFetchManagers } from '../hooks/useFetchManagers';
import { DataTableSkeleton } from '@/components/shared/data-table-skeleton';
import ManagerTableActions from './table/manager-table-action';
import { Manager } from '../lib/types';

export default function ManagersTable() {
  const [page, setPage] = useState(1);
  const {
    managers,
    pagination,
    isLoading
  }: { managers: Manager[]; pagination: any; isLoading: boolean } =
    useFetchManagers(page);
  useEffect(() => {
    if (!isLoading) {
      setPage(pagination?.page);
    }
  }, [pagination?.page, isLoading]);

  const pageLimit = pagination?.per_page;
  const totalCountries = pagination?.total;
  const pageCount = Math.ceil(totalCountries! / pageLimit);

  return (
    <>
      <ManagerTableActions />
      {isLoading ? (
        <div className="p-5">
          <DataTableSkeleton columnCount={6} />
        </div>
      ) : (
        <DataTable
          columns={columns}
          data={managers}
          pageCount={pageCount}
          onPageChange={setPage}
        />
      )}
    </>
  );
}
