import { useQuery } from '@tanstack/react-query';
import { client, setHeaderToken, refreshAuth } from '../../../lib/axiosClient';
import { redirect } from 'react-router-dom';
import toast from 'react-hot-toast';

// Manager Details Response interface
export interface ManagerDetailsResponse {
  status: boolean;
  message: string;
  data: {
    id: number;
    username: string;
    email: string;
    status: 'ACTIVE' | 'INACTIVE';
    profile_image: {
      path: string;
      url: string;
    };
  };
}

const fetchManagerDetails = async (
  managerId: number
): Promise<ManagerDetailsResponse> => {
  const token = localStorage.getItem('token');

  try {
    const response = await client.get(`/admin/managers/${managerId}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    return response.data;
  } catch (error: any) {
    if (error.response?.status === 401) {
      const newToken = await refreshAuth();
      if (newToken) {
        setHeaderToken(newToken);
        const retryResponse = await client.get(`/admin/managers/${managerId}`, {
          headers: {
            Authorization: `Bearer ${newToken}`
          }
        });
        return retryResponse.data;
      } else {
        localStorage.removeItem('token');
        toast.error('Authentication failed');
        redirect('/login');
        throw new Error('Authentication failed');
      }
    } else {
      throw error;
    }
  }
};

export function useFetchManagerDetails(managerId: number) {
  return useQuery({
    queryKey: ['manager-details', managerId],
    queryFn: () => fetchManagerDetails(managerId),
    enabled: !!managerId,
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: 1
  });
}
