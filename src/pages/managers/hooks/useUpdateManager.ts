import { useMutation, useQueryClient } from '@tanstack/react-query';
import { client, setHeaderToken, refreshAuth } from '../../../lib/axiosClient';
import { redirect } from 'react-router-dom';
import toast from 'react-hot-toast';

const updateManager = async ({
  id,
  data
}: {
  id: number;
  data: {
    username: string;
    email: string;
    profile_image: string;
  };
}) => {
  const token = localStorage.getItem('token');

  try {
    const response = await client.put(`/admin/managers/${id}`, data, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });
    return response.data;
  } catch (error: any) {
    if (error.response?.status === 401) {
      const newToken = await refreshAuth();
      if (newToken) {
        setHeaderToken(newToken);
        const retryResponse = await client.put(`/admin/managers/${id}`, data, {
          headers: {
            Authorization: `Bearer ${newToken}`
          }
        });
        return retryResponse.data;
      } else {
        localStorage.removeItem('token');
        toast.error('something went wrong');
        redirect('/login');
      }
    } else if (error.response?.status === 422) {
      console.log('Validation Error:', error.response.data);
      throw new Error(
        'Validation failed: ' + JSON.stringify(error.response.data)
      );
    } else {
      throw error;
    }
  }
};

export function useUpdateManager() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: updateManager,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['managers'] });
    }
  });
}
