import PageHead from '@/components/shared/page-head';
import TripsTable from './components';
import { Breadcrumbs } from '@/components/shared/breadcrumbs';
import { useTranslation } from 'react-i18next';

export default function TripsPage() {
  const { t } = useTranslation();

  return (
    <div className="p-4 md:p-8">
      <PageHead title="Trips Management | App" />
      <Breadcrumbs
        items={[
          { title: 'Dashboard', link: '/' },
          { title: t('sidebar.trips'), link: '/trips' }
        ]}
      />

      <TripsTable />
    </div>
  );
}
