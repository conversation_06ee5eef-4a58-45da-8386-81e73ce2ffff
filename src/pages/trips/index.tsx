import PageHead from '@/components/shared/page-head';
import TripsTable from './components';
import { Breadcrumbs } from '@/components/shared/breadcrumbs';
import { useTranslation } from 'react-i18next';

export default function TripsPage() {
  const { t } = useTranslation();

  return (
    <div className="flex flex-col h-full p-4 md:p-8">
      <PageHead title="Trips Management | App" />
      <div className="mb-6">
        <Breadcrumbs
          items={[
            { title: 'Dashboard', link: '/' },
            { title: t('sidebar.trips'), link: '/trips' }
          ]}
        />
      </div>

      <div className="flex-1 min-h-0">
        <TripsTable />
      </div>
    </div>
  );
}
