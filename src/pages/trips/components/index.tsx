import DataTable from '@/components/shared/data-table';
import { TripFilters } from '../lib/types';
import { createColumns } from './table/columns';
import { useState, useCallback, useEffect, useMemo } from 'react';
import { useFetchTrips } from '../hooks/useFetchTrips';
import { DataTableSkeleton } from '@/components/shared/data-table-skeleton';
import TripFiltersComponent from './table/trip-filters';
import { useTranslation } from 'react-i18next';

export default function TripsTable() {
  const { i18n } = useTranslation();
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState<TripFilters>({
    page: 1,
    page_size: 10,
  });

  // Create columns that update when language changes
  const columns = useMemo(() => createColumns(i18n.language), [i18n.language]);

  const {
    trips,
    pagination,
    isLoading,
    error,
    refetch
  } = useFetchTrips({
    ...filters,
    page,
  });

  const handleFiltersChange = useCallback((newFilters: TripFilters) => {
    setFilters(newFilters);
    setPage(1); // Reset to first page when filters change
  }, []);

  const handleClearFilters = useCallback(() => {
    const clearedFilters: TripFilters = {
      page: 1,
      page_size: 10,
      keyword: undefined,
      status: undefined,
      trip_type: undefined,
      city_id: undefined,
      from_date: undefined,
      to_date: undefined,
      order_by: undefined,
      order: undefined,
    };
    setFilters(clearedFilters);
    setPage(1);
  }, []);

  const handlePageChange = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  // Refetch when language changes
  useEffect(() => {
    refetch();
  }, [i18n.language, refetch]);

  const pageCount = pagination ? Math.ceil(pagination.total / pagination.per_page) : 0;

  if (error) {
    return (
      <div className="p-8 text-center">
        <p className="text-red-500">Error loading trips: {error.message}</p>
        <button
          onClick={() => refetch()}
          className="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <TripFiltersComponent
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onClearFilters={handleClearFilters}
      />

      {isLoading ? (
        <div className="p-5">
          <DataTableSkeleton columnCount={10} />
        </div>
      ) : (
        <DataTable
          columns={columns}
          data={trips}
          pageCount={pageCount}
          onPageChange={handlePageChange}
        />
      )}
    </div>
  );
}
