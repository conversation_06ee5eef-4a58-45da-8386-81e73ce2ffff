import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu';
import { MoreHorizontal, MapPin, User } from 'lucide-react';
import { Trip } from '../../lib/types';
import PopupModal from '@/components/shared/popup-modal';
import TripDetailsModal from '../modals/trip-details-modal';

interface CellActionProps {
  data: Trip;
}

export const CellAction: React.FC<CellActionProps> = ({ data }) => {
  return (
    <DropdownMenu modal={false}>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="h-8 w-8 p-0">
          <span className="sr-only">Open menu</span>
          <MoreHorizontal className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Actions</DropdownMenuLabel>

        <div className="flex flex-col gap-1">
          <PopupModal
            title="View Details"
            icon="Eye"
            renderModal={(onClose) => (
              <TripDetailsModal trip={data} onClose={onClose} />
            )}
          />

          <DropdownMenuItem
            onClick={() => {
              // Copy trip ID to clipboard
              navigator.clipboard.writeText(data.id.toString());
            }}
            className="cursor-pointer"
          >
            <User className="mr-2 h-4 w-4" />
            Copy Trip ID
          </DropdownMenuItem>

          {data.source.location && (
            <DropdownMenuItem
              onClick={() => {
                // Open location in maps
                const { latitude, longitude } = data.source.location;
                window.open(`https://maps.google.com/?q=${latitude},${longitude}`, '_blank');
              }}
              className="cursor-pointer"
            >
              <MapPin className="mr-2 h-4 w-4" />
              View Source Location
            </DropdownMenuItem>
          )}

          {data.destinations[0]?.location && (
            <DropdownMenuItem
              onClick={() => {
                // Open destination in maps
                const { latitude, longitude } = data.destinations[0].location;
                window.open(`https://maps.google.com/?q=${latitude},${longitude}`, '_blank');
              }}
              className="cursor-pointer"
            >
              <MapPin className="mr-2 h-4 w-4" />
              View Destination
            </DropdownMenuItem>
          )}
        </div>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
