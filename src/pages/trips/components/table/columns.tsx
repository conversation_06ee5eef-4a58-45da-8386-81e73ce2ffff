import { Checkbox } from '@/components/ui/checkbox';
import { ColumnDef } from '@tanstack/react-table';
import { CellAction } from './cell-action';
import { Trip } from '../../lib/types';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';

const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
  switch (status.toLowerCase()) {
    case 'completed':
      return 'default'; // Green-ish
    case 'canceled':
    case 'cancelled':
      return 'destructive'; // Red
    case 'pending':
      return 'secondary'; // Yellow-ish
    case 'started':
    case 'fulfilled':
      return 'outline'; // Blue-ish
    default:
      return 'secondary';
  }
};

const getTripTypeBadgeVariant = (type: string): "default" | "secondary" | "destructive" | "outline" => {
  switch (type.toLowerCase()) {
    case 'normal':
      return 'default';
    case 'scheduled':
      return 'outline';
    default:
      return 'secondary';
  }
};

export const columns: ColumnDef<Trip>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'id',
    header: 'Trip ID',
    cell: ({ row }) => (
      <div className="font-medium">#{row.original.id}</div>
    )
  },
  {
    accessorKey: 'driver',
    header: 'Captain',
    cell: ({ row }) => (
      <div className="max-w-[150px] truncate">
        {row.original.driver?.name || 'No Captain'}
      </div>
    )
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => (
      <Badge variant={getStatusBadgeVariant(row.original.status)}>
        {row.original.status}
      </Badge>
    )
  },
  {
    accessorKey: 'trip_type',
    header: 'Type',
    cell: ({ row }) => (
      <Badge variant={getTripTypeBadgeVariant(row.original.trip_type)}>
        {row.original.trip_type}
      </Badge>
    )
  },
  {
    accessorKey: 'initiator_name',
    header: 'Initiator',
    cell: ({ row }) => (
      <div className="max-w-[150px]">
        <div className="truncate font-medium">{row.original.initiator_name}</div>
        <div className="text-xs text-muted-foreground">{row.original.initiator_type}</div>
      </div>
    )
  },
  {
    accessorKey: 'source_name',
    header: 'From',
    cell: ({ row }) => (
      <div className="max-w-[120px] truncate" title={row.original.source_name}>
        {row.original.source_name}
      </div>
    )
  },
  {
    accessorKey: 'destinations_name',
    header: 'To',
    cell: ({ row }) => (
      <div className="max-w-[120px] truncate" title={row.original.destinations_name}>
        {row.original.destinations_name}
      </div>
    )
  },
  {
    accessorKey: 'date',
    header: 'Date',
    cell: ({ row }) => (
      <div className="text-sm">
        {format(new Date(row.original.date), 'MMM dd, yyyy HH:mm')}
      </div>
    )
  },
  {
    accessorKey: 'total_amount',
    header: 'Amount',
    cell: ({ row }) => (
      <div className="font-medium">
        {row.original.total_amount ? `${row.original.total_amount}` : 'N/A'}
      </div>
    )
  },
  {
    id: 'actions',
    cell: ({ row }) => <CellAction data={row.original} />
  }
];
