import PopupModal from '@/components/shared/popup-modal';
import TableSearchInput from '@/components/shared/table-search-input';
import CountryCreateForm from '../forms/city-form';
import { usePermissions } from '@/contexts/PermissionsContext';

export default function CityTableActions() {
  const { hasPermission } = usePermissions();
  const canCreate = hasPermission('cities', 'create');

  return (
    <div className="flex items-center justify-between gap-2 py-5">
      <div className="flex flex-1 gap-4">
        <TableSearchInput placeholder="Search Cities Here" />
      </div>
      <div className="flex gap-3">
        {canCreate && (
          <PopupModal
            renderModal={(onClose) => (
              <CountryCreateForm modalClose={onClose} />
            )}
          />
        )}
      </div>
    </div>
  );
}
