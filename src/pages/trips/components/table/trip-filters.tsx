import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon, Filter, X } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { TripFilters } from '../../lib/types';
import { useFetchCities } from '../../hooks/useFetchCities';
import { useTranslation } from 'react-i18next';

interface TripFiltersProps {
  filters: TripFilters;
  onFiltersChange: (filters: TripFilters) => void;
  onClearFilters: () => void;
}

const statusOptions = [
  { value: 'all', label: 'All Statuses' },
  { value: 'Pending', label: 'Pending' },
  { value: 'Started', label: 'Started' },
  { value: 'Fulfilled', label: 'Fulfilled' },
  { value: 'Completed', label: 'Completed' },
  { value: 'Canceled', label: 'Canceled' },
];

const tripTypeOptions = [
  { value: 'all', label: 'All Types' },
  { value: 'Normal', label: 'Normal' },
  { value: 'Scheduled', label: 'Scheduled' },
];

const sortOptions = [
  { value: 'none', label: 'Default' },
  { value: 'date', label: 'Date' },
  { value: 'id', label: 'Trip ID' },
  { value: 'status', label: 'Status' },
  { value: 'total_amount', label: 'Amount' },
];

const orderOptions = [
  { value: 'desc', label: 'Descending' },
  { value: 'asc', label: 'Ascending' },
];

export default function TripFiltersComponent({ filters, onFiltersChange, onClearFilters }: TripFiltersProps) {
  const { i18n } = useTranslation();
  const { cities, isLoading: citiesLoading, hasError: citiesError } = useFetchCities();

  const [fromDate, setFromDate] = useState<Date | undefined>(
    filters.from_date ? new Date(filters.from_date) : undefined
  );
  const [toDate, setToDate] = useState<Date | undefined>(
    filters.to_date ? new Date(filters.to_date) : undefined
  );

  const handleFilterChange = (key: keyof TripFilters, value: string | number | undefined) => {
    const newFilters = {
      ...filters,
      [key]: value,
    };

    // If order_by is cleared, also clear order
    if (key === 'order_by' && (value === undefined || value === 'none')) {
      newFilters.order = undefined;
    }

    onFiltersChange(newFilters);
  };

  const handleDateChange = (type: 'from' | 'to', date: Date | undefined) => {
    if (type === 'from') {
      setFromDate(date);
      handleFilterChange('from_date', date ? format(date, 'yyyy-MM-dd') : undefined);
    } else {
      setToDate(date);
      handleFilterChange('to_date', date ? format(date, 'yyyy-MM-dd') : undefined);
    }
  };

  const hasActiveFilters = Object.values(filters).some(value => 
    value !== undefined && value !== '' && value !== null
  );

  return (
    <div className="space-y-4 p-4 border rounded-lg bg-muted/50">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4" />
          <h3 className="font-medium">Filters</h3>
        </div>
        
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="h-8 px-2 lg:px-3"
          >
            <X className="h-4 w-4 mr-1" />
            Clear
          </Button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Keyword Search */}
        <div className="space-y-2">
          <Label htmlFor="keyword">Search</Label>
          <Input
            id="keyword"
            placeholder="Search trips..."
            value={filters.keyword || ''}
            onChange={(e) => handleFilterChange('keyword', e.target.value)}
          />
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <Label>Status</Label>
          <Select
            value={filters.status || 'all'}
            onValueChange={(value) => handleFilterChange('status', value === 'all' ? undefined : value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Trip Type Filter */}
        <div className="space-y-2">
          <Label>Trip Type</Label>
          <Select
            value={filters.trip_type || 'all'}
            onValueChange={(value) => handleFilterChange('trip_type', value === 'all' ? undefined : value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select type" />
            </SelectTrigger>
            <SelectContent>
              {tripTypeOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* City Filter */}
        <div className="space-y-2">
          <Label>City</Label>
          {citiesError ? (
            // Fallback to text input when cities API fails (CORS or other errors)
            <Input
              type="number"
              placeholder="Enter city ID"
              value={filters.city_id || ''}
              onChange={(e) => handleFilterChange('city_id', e.target.value ? parseInt(e.target.value) : undefined)}
            />
          ) : (
            // Use select dropdown when cities are available
            <Select
              value={filters.city_id ? filters.city_id.toString() : 'all'}
              onValueChange={(value) => handleFilterChange('city_id', value === 'all' ? undefined : parseInt(value))}
              disabled={citiesLoading}
            >
              <SelectTrigger>
                <SelectValue placeholder={citiesLoading ? "Loading cities..." : "Select city"} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Cities</SelectItem>
                {cities.map((city) => (
                  <SelectItem key={city.id} value={city.id.toString()}>
                    {i18n.language === 'ar' ? city.ar_name : city.en_name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* From Date */}
        <div className="space-y-2">
          <Label>From Date</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !fromDate && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {fromDate ? format(fromDate, "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={fromDate}
                onSelect={(date) => handleDateChange('from', date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* To Date */}
        <div className="space-y-2">
          <Label>To Date</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal",
                  !toDate && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {toDate ? format(toDate, "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={toDate}
                onSelect={(date) => handleDateChange('to', date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Sort By */}
        <div className="space-y-2">
          <Label>Sort By</Label>
          <Select
            value={filters.order_by || 'none'}
            onValueChange={(value) => handleFilterChange('order_by', value === 'none' ? undefined : value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Sort Direction */}
        <div className="space-y-2">
          <Label>Order</Label>
          <Select
            value={filters.order || 'desc'}
            onValueChange={(value) => handleFilterChange('order', value as 'asc' | 'desc')}
          >
            <SelectTrigger>
              <SelectValue placeholder="Order" />
            </SelectTrigger>
            <SelectContent>
              {orderOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}
