import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon, Search, SlidersHorizontal, X, MapPin, Filter } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { TripFilters } from '../../lib/types';
import { useFetchCities } from '../../hooks/useFetchCities';
import { useTranslation } from 'react-i18next';
import { Badge } from '@/components/ui/badge';

interface TripFiltersProps {
  filters: TripFilters;
  onFiltersChange: (filters: TripFilters) => void;
  onClearFilters: () => void;
}

const statusOptions = [
  { value: 'all', label: 'All Statuses', arLabel: 'جميع الحالات' },
  { value: 'Pending', label: 'Pending', arLabel: 'في الانتظار' },
  { value: 'Started', label: 'Started', arLabel: 'بدأت' },
  { value: 'Fulfilled', label: 'Fulfilled', arLabel: 'تم التنفيذ' },
  { value: 'Completed', label: 'Completed', arLabel: 'مكتملة' },
  { value: 'Canceled', label: 'Canceled', arLabel: 'ملغية' },
];

const tripTypeOptions = [
  { value: 'all', label: 'All Types', arLabel: 'جميع الأنواع' },
  { value: 'Normal', label: 'Normal', arLabel: 'عادية' },
  { value: 'Scheduled', label: 'Scheduled', arLabel: 'مجدولة' },
];

const sortOptions = [
  { value: 'none', label: 'Default' },
  { value: 'date', label: 'Date' },
  { value: 'id', label: 'Trip ID' },
  { value: 'status', label: 'Status' },
  { value: 'total_amount', label: 'Amount' },
];

const orderOptions = [
  { value: 'desc', label: 'Descending' },
  { value: 'asc', label: 'Ascending' },
];

// Helper functions for colored badges
const getStatusBadgeVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
  switch (status.toLowerCase()) {
    case 'completed':
      return 'default'; // Green-ish
    case 'canceled':
    case 'cancelled':
      return 'destructive'; // Red
    case 'pending':
      return 'secondary'; // Yellow-ish
    case 'started':
    case 'fulfilled':
      return 'outline'; // Blue-ish
    default:
      return 'secondary';
  }
};

const getTripTypeBadgeVariant = (type: string): "default" | "secondary" | "destructive" | "outline" => {
  switch (type.toLowerCase()) {
    case 'normal':
      return 'default';
    case 'scheduled':
      return 'outline';
    default:
      return 'secondary';
  }
};

const getStatusLabel = (status: string, isArabic: boolean) => {
  const option = statusOptions.find(opt => opt.value === status);
  return option ? (isArabic ? option.arLabel : option.label) : status;
};

const getTripTypeLabel = (type: string, isArabic: boolean) => {
  const option = tripTypeOptions.find(opt => opt.value === type);
  return option ? (isArabic ? option.arLabel : option.label) : type;
};

export default function TripFiltersComponent({ filters, onFiltersChange, onClearFilters }: TripFiltersProps) {
  const { i18n } = useTranslation();
  const { cities } = useFetchCities();
  const [isExpanded, setIsExpanded] = useState(false);

  const [fromDate, setFromDate] = useState<Date | undefined>(
    filters.from_date ? new Date(filters.from_date) : undefined
  );
  const [toDate, setToDate] = useState<Date | undefined>(
    filters.to_date ? new Date(filters.to_date) : undefined
  );

  const handleFilterChange = (key: keyof TripFilters, value: string | number | undefined) => {
    const newFilters = {
      ...filters,
      [key]: value,
    };

    // If order_by is cleared, also clear order
    if (key === 'order_by' && (value === undefined || value === 'none')) {
      newFilters.order = undefined;
    }

    onFiltersChange(newFilters);
  };

  const handleDateChange = (type: 'from' | 'to', date: Date | undefined) => {
    if (type === 'from') {
      setFromDate(date);
      handleFilterChange('from_date', date ? format(date, 'yyyy-MM-dd') : undefined);
    } else {
      setToDate(date);
      handleFilterChange('to_date', date ? format(date, 'yyyy-MM-dd') : undefined);
    }
  };

  const getActiveFiltersCount = () => {
    return Object.entries(filters).filter(([key, value]) =>
      key !== 'page' && key !== 'page_size' && value !== undefined && value !== '' && value !== null
    ).length;
  };

  const activeFiltersCount = getActiveFiltersCount();

  const renderActiveFilterBadges = () => {
    const badges: JSX.Element[] = [];
    const isArabic = i18n.language === 'ar';

    if (filters.keyword) {
      badges.push(
        <Badge key="keyword" variant="secondary" className="gap-1">
          <Search className="h-3 w-3" />
          {filters.keyword}
          <X
            className="h-3 w-3 cursor-pointer hover:bg-muted-foreground/20 rounded-full"
            onClick={() => handleFilterChange('keyword', undefined)}
          />
        </Badge>
      );
    }

    if (filters.status) {
      const statusLabel = getStatusLabel(filters.status, isArabic);
      badges.push(
        <Badge key="status" variant={getStatusBadgeVariant(filters.status)} className="gap-1">
          <Filter className="h-3 w-3" />
          {statusLabel}
          <X
            className="h-3 w-3 cursor-pointer hover:bg-muted-foreground/20 rounded-full"
            onClick={() => handleFilterChange('status', undefined)}
          />
        </Badge>
      );
    }

    if (filters.trip_type) {
      const typeLabel = getTripTypeLabel(filters.trip_type, isArabic);
      badges.push(
        <Badge key="trip_type" variant={getTripTypeBadgeVariant(filters.trip_type)} className="gap-1">
          {typeLabel}
          <X
            className="h-3 w-3 cursor-pointer hover:bg-muted-foreground/20 rounded-full"
            onClick={() => handleFilterChange('trip_type', undefined)}
          />
        </Badge>
      );
    }

    if (filters.city_id) {
      const cityName = cities.find(city => city.id === filters.city_id);
      badges.push(
        <Badge key="city" variant="secondary" className="gap-1">
          <MapPin className="h-3 w-3" />
          {cityName ? (i18n.language === 'ar' ? cityName.ar_name : cityName.en_name) : `City ${filters.city_id}`}
          <X
            className="h-3 w-3 cursor-pointer hover:bg-muted-foreground/20 rounded-full"
            onClick={() => handleFilterChange('city_id', undefined)}
          />
        </Badge>
      );
    }

    if (filters.from_date || filters.to_date) {
      badges.push(
        <Badge key="dates" variant="secondary" className="gap-1">
          <CalendarIcon className="h-3 w-3" />
          {filters.from_date && filters.to_date
            ? `${format(new Date(filters.from_date), 'MMM dd')} - ${format(new Date(filters.to_date), 'MMM dd')}`
            : filters.from_date
            ? `From ${format(new Date(filters.from_date), 'MMM dd')}`
            : `Until ${format(new Date(filters.to_date!), 'MMM dd')}`
          }
          <X
            className="h-3 w-3 cursor-pointer hover:bg-muted-foreground/20 rounded-full"
            onClick={() => {
              handleFilterChange('from_date', undefined);
              handleFilterChange('to_date', undefined);
              setFromDate(undefined);
              setToDate(undefined);
            }}
          />
        </Badge>
      );
    }

    return badges;
  };

  return (
    <div className="relative">
      {/* Main Search Bar */}
      <div className="flex items-center gap-3 relative z-10">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search trips by ID, customer name, or location..."
            value={filters.keyword || ''}
            onChange={(e) => handleFilterChange('keyword', e.target.value)}
            className="pl-10 h-10 border-0 bg-muted/50 focus:bg-background focus:ring-1 focus:ring-ring transition-colors"
          />
        </div>

        <Button
          variant="ghost"
          onClick={() => setIsExpanded(!isExpanded)}
          className={cn(
            "h-10 px-3 gap-2 transition-all duration-200 hover:bg-muted",
            activeFiltersCount > 0 && "bg-primary/10 text-primary hover:bg-primary/20"
          )}
        >
          <SlidersHorizontal className="h-4 w-4" />
          <span className="hidden sm:inline">Filters</span>
          {activeFiltersCount > 0 && (
            <div className="h-5 w-5 rounded-full bg-primary text-primary-foreground text-xs flex items-center justify-center">
              {activeFiltersCount}
            </div>
          )}
        </Button>

        {activeFiltersCount > 0 && (
          <Button
            variant="ghost"
            size="sm"
            onClick={onClearFilters}
            className="h-10 w-10 p-0 text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
          >
            <X className="h-4 w-4" />
          </Button>
        )}
      </div>

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2 mt-4 relative z-10">
          {renderActiveFilterBadges()}
        </div>
      )}

      {/* Expanded Filters Overlay */}
      {isExpanded && (
        <div className="absolute top-full left-0 right-0 z-20 mt-2">
          <div className="rounded-xl border bg-background/95 backdrop-blur-md shadow-lg p-6 space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {/* Status Filter */}
              <div className="space-y-3">
                <label className="text-sm font-medium text-muted-foreground">Status</label>
                <Select
                  value={filters.status || 'all'}
                  onValueChange={(value) => handleFilterChange('status', value === 'all' ? undefined : value)}
                >
                  <SelectTrigger className="h-10 border-0 bg-background/60 hover:bg-background focus:bg-background transition-colors">
                    <SelectValue placeholder="All statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    {statusOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {i18n.language === 'ar' ? option.arLabel : option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Trip Type Filter */}
              <div className="space-y-3">
                <label className="text-sm font-medium text-muted-foreground">Trip Type</label>
                <Select
                  value={filters.trip_type || 'all'}
                  onValueChange={(value) => handleFilterChange('trip_type', value === 'all' ? undefined : value)}
                >
                  <SelectTrigger className="h-10 border-0 bg-background/60 hover:bg-background focus:bg-background transition-colors">
                    <SelectValue placeholder="All types" />
                  </SelectTrigger>
                  <SelectContent>
                    {tripTypeOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {i18n.language === 'ar' ? option.arLabel : option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* City Filter */}
              <div className="space-y-3">
                <label className="text-sm font-medium text-muted-foreground">City</label>
                <Select
                  value={filters.city_id ? filters.city_id.toString() : 'all'}
                  onValueChange={(value) => handleFilterChange('city_id', value === 'all' ? undefined : parseInt(value))}
                >
                  <SelectTrigger className="h-10 border-0 bg-background/60 hover:bg-background focus:bg-background transition-colors">
                    <SelectValue placeholder="All cities" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Cities</SelectItem>
                    {cities.map((city) => (
                      <SelectItem key={city.id} value={city.id.toString()}>
                        {i18n.language === 'ar' ? city.ar_name : city.en_name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {/* From Date */}
              <div className="space-y-3">
                <label className="text-sm font-medium text-muted-foreground">From Date</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal h-10 border-0 bg-background/60 hover:bg-background transition-colors",
                        !fromDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {fromDate ? format(fromDate, "MMM dd, yyyy") : "Select date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={fromDate}
                      onSelect={(date) => handleDateChange('from', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* To Date */}
              <div className="space-y-3">
                <label className="text-sm font-medium text-muted-foreground">To Date</label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal h-10 border-0 bg-background/60 hover:bg-background transition-colors",
                        !toDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {toDate ? format(toDate, "MMM dd, yyyy") : "Select date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={toDate}
                      onSelect={(date) => handleDateChange('to', date)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              {/* Sort By */}
              <div className="space-y-3">
                <label className="text-sm font-medium text-muted-foreground">Sort By</label>
                <Select
                  value={filters.order_by || 'none'}
                  onValueChange={(value) => handleFilterChange('order_by', value === 'none' ? undefined : value)}
                >
                  <SelectTrigger className="h-10 border-0 bg-background/60 hover:bg-background focus:bg-background transition-colors">
                    <SelectValue placeholder="Default" />
                  </SelectTrigger>
                  <SelectContent>
                    {sortOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Sort Direction */}
              <div className="space-y-3">
                <label className="text-sm font-medium text-muted-foreground">Order</label>
                <Select
                  value={filters.order || 'desc'}
                  onValueChange={(value) => handleFilterChange('order', value as 'asc' | 'desc')}
                >
                  <SelectTrigger className="h-10 border-0 bg-background/60 hover:bg-background focus:bg-background transition-colors">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {orderOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
