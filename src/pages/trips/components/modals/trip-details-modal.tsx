import { Trip } from '../../lib/types';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { MapPin, User, Car, Clock, DollarSign, Phone } from 'lucide-react';
import { Separator } from '@/components/ui/separator';

interface TripDetailsModalProps {
  trip: Trip;
  onClose: () => void;
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'completed':
      return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
    case 'canceled':
    case 'cancelled':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
    case 'started':
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
    case 'fulfilled':
      return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';
  }
};

export default function TripDetailsModal({ trip, onClose }: TripDetailsModalProps) {
  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold">Trip Details #{trip.id}</h2>
        <div className="flex gap-2">
          <Badge className={getStatusColor(trip.status)}>
            {trip.status}
          </Badge>
          <Badge variant="outline">
            {trip.trip_type}
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Trip Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Clock className="h-5 w-5" />
            Trip Information
          </h3>
          
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-muted-foreground">Date:</span>
              <span>{format(new Date(trip.date), 'MMM dd, yyyy HH:mm')}</span>
            </div>
            
            {trip.schedule_time && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Scheduled:</span>
                <span>{format(new Date(trip.schedule_time), 'MMM dd, yyyy HH:mm')}</span>
              </div>
            )}
            
            <div className="flex justify-between">
              <span className="text-muted-foreground">City:</span>
              <span>{trip.city}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-muted-foreground">Company:</span>
              <span>{trip.company_name}</span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-muted-foreground">Payment:</span>
              <span>{trip.payment_way_by_driver}</span>
            </div>
          </div>
        </div>

        {/* Customer & Driver Information */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <User className="h-5 w-5" />
            People
          </h3>
          
          <div className="space-y-3">
            <div>
              <h4 className="font-medium">Customer</h4>
              <p className="text-sm text-muted-foreground">{trip.customer.name}</p>
              <p className="text-xs text-muted-foreground">
                Navigation: {trip.customer.can_navigate ? 'Available' : 'Not Available'}
              </p>
            </div>
            
            <div>
              <h4 className="font-medium">Driver</h4>
              <p className="text-sm text-muted-foreground">
                {trip.driver?.name || 'No driver assigned'}
              </p>
            </div>
            
            <div>
              <h4 className="font-medium">Initiator</h4>
              <p className="text-sm text-muted-foreground">{trip.initiator_name}</p>
              <p className="text-xs text-muted-foreground">{trip.initiator_type}</p>
            </div>
          </div>
        </div>
      </div>

      <Separator />

      {/* Location Information */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold flex items-center gap-2">
          <MapPin className="h-5 w-5" />
          Route
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium text-green-600 mb-2">From</h4>
            <p className="text-sm">{trip.source_name}</p>
            <p className="text-xs text-muted-foreground">
              {trip.source.location.latitude}, {trip.source.location.longitude}
            </p>
          </div>
          
          <div className="p-4 border rounded-lg">
            <h4 className="font-medium text-red-600 mb-2">To</h4>
            <p className="text-sm">{trip.destinations_name}</p>
            {trip.destinations[0] && (
              <p className="text-xs text-muted-foreground">
                {trip.destinations[0].location.latitude}, {trip.destinations[0].location.longitude}
              </p>
            )}
          </div>
        </div>
      </div>

      <Separator />

      {/* Car Type & Pricing */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <Car className="h-5 w-5" />
            Vehicle
          </h3>
          
          <div className="flex items-center gap-3">
            {trip.car_type.image && (
              <img 
                src={trip.car_type.image.path} 
                alt={trip.car_type.name}
                className="w-12 h-12 object-contain"
              />
            )}
            <div>
              <p className="font-medium">{trip.car_type.name}</p>
              <p className="text-sm text-muted-foreground">{trip.car_type.vehicle_type}</p>
            </div>
          </div>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Pricing
          </h3>
          
          <div className="space-y-2">
            {trip.total_amount && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Total Amount:</span>
                <span className="font-medium">{trip.total_amount}</span>
              </div>
            )}
            
            {trip.price_after_fixing && (
              <div className="flex justify-between">
                <span className="text-muted-foreground">Fixed Price:</span>
                <span className="font-medium">{trip.price_after_fixing}</span>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Cancellation Information */}
      {trip.status.toLowerCase().includes('cancel') && (
        <>
          <Separator />
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-red-600">Cancellation Details</h3>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-muted-foreground">Canceled by:</span>
                <span>{trip.canceled_by}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-muted-foreground">Reason:</span>
                <span>{trip.cancellation_reason}</span>
              </div>
              
              {trip.other_cancellation_reason && (
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Other reason:</span>
                  <span>{trip.other_cancellation_reason}</span>
                </div>
              )}
            </div>
          </div>
        </>
      )}

      {/* Trip Details */}
      {trip.details && trip.details.length > 0 && (
        <>
          <Separator />
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Trip Progress</h3>
            
            <div className="space-y-3">
              {trip.details.map((detail, index) => (
                <div key={detail.id} className="flex items-center gap-3 p-3 border rounded-lg">
                  <div className="flex-shrink-0">
                    <Badge className={getStatusColor(detail.status)}>
                      {detail.status}
                    </Badge>
                  </div>
                  
                  <div className="flex-1 grid grid-cols-2 md:grid-cols-4 gap-2 text-sm">
                    <div>
                      <span className="text-muted-foreground">Time:</span>
                      <p>{detail.actual_time || detail.expected_time}min</p>
                    </div>
                    
                    <div>
                      <span className="text-muted-foreground">Distance:</span>
                      <p>{detail.actual_distance || detail.expected_distance}m</p>
                    </div>
                    
                    <div>
                      <span className="text-muted-foreground">Price:</span>
                      <p>{detail.expected_price}</p>
                    </div>
                    
                    <div>
                      <span className="text-muted-foreground">Date:</span>
                      <p>{format(new Date(detail.date), 'HH:mm')}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </>
      )}

      {trip.notes && (
        <>
          <Separator />
          <div className="space-y-2">
            <h3 className="text-lg font-semibold">Notes</h3>
            <p className="text-sm text-muted-foreground">{trip.notes}</p>
          </div>
        </>
      )}
    </div>
  );
}
