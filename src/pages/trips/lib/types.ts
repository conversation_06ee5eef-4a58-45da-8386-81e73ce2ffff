export interface Location {
  latitude: number;
  longitude: number;
}

export interface Address {
  address: string;
  location: Location;
}

export interface Customer {
  id: number;
  name: string;
  can_navigate: boolean;
}

export interface Driver {
  id: number;
  name: string;
}

export interface CarType {
  id: number;
  name: string;
  vehicle_type: string;
  vehicle_type_to_select: string;
  ar_name: string;
  en_name: string;
  image: {
    id: number;
    path: string;
  };
}

export interface Company {
  id: number;
  name: string;
  rate: string;
}

export interface Initiator {
  id: number;
  name: string;
}

export interface TripDetail {
  id: number;
  status: string;
  status_to_select: string;
  actual_path: string | null;
  actual_time: string;
  actual_distance: string;
  expected_path: string | null;
  expected_time: string;
  expected_distance: string;
  expected_price: string;
  date: string;
}

export interface Trip {
  id: number;
  company_name: string;
  city: string;
  trip_type: string;
  trip_type_to_select: string;
  schedule_time: string | null;
  source_name: string;
  source: Address;
  destinations_name: string;
  destinations: Address[];
  customer: Customer;
  driver: Driver | null;
  notes: string | null;
  trip_rates: any | null;
  date: string;
  car_type: CarType;
  company: Company;
  city_id: number;
  status: string;
  status_to_select: string;
  canceled_by: string | null;
  responsible: any | null;
  initiator: Initiator;
  initiator_name: string;
  initiator_type: string;
  cancellation_reason: string | null;
  other_cancellation_reason: string | null;
  details: TripDetail[];
  is_company_trip: boolean;
  payment_way_by_driver: string;
  formula_string: string;
  total_amount: number | null;
  price_after_fixing: number | null;
}

export interface TripsResponse {
  errors: any[];
  data: {
    data: Trip[];
    pagination: {
      total: number;
      count: number;
      per_page: number;
      current_page: number;
      total_pages: number;
    };
  };
  message: string;
  code: number;
}

export interface City {
  id: number;
  name: string;
  ar_name: string;
  en_name: string;
}

export interface CitiesResponse {
  errors: any[];
  data: {
    data: City[];
    pagination: {
      total: number;
      count: number;
      per_page: number;
      current_page: number;
      total_pages: number;
    };
  };
  message: string;
  code: number;
}

export interface TripFilters {
  page?: number;
  page_size?: number;
  keyword?: string;
  status?: string;
  trip_type?: string;
  city_id?: number;
  from_date?: string;
  to_date?: string;
  order?: 'asc' | 'desc';
  order_by?: string;
}
