import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { getAuthToken } from '../../../lib/cookies';
import { TripFilters, TripsResponse } from '../lib/types';
import { useTranslation } from 'react-i18next';

export const fetchTrips = async (filters: TripFilters & { language?: string } = {}): Promise<TripsResponse> => {
  const token = getAuthToken();

  if (!token) {
    throw new Error('No authentication token found');
  }

  // Build query parameters
  const params = new URLSearchParams();

  // Add pagination
  params.append('page_size', (filters.page_size || 10).toString());
  if (filters.page) params.append('page', filters.page.toString());

  // Add filters
  if (filters.keyword) params.append('keyword', filters.keyword);
  if (filters.status) params.append('status', filters.status);
  if (filters.trip_type) params.append('trip_type', filters.trip_type);
  if (filters.city_id) params.append('city_id', filters.city_id.toString());
  if (filters.from_date) params.append('from_date', filters.from_date);
  if (filters.to_date) params.append('to_date', filters.to_date);

  // Add sorting - both parameters are required together
  if (filters.order_by) {
    params.append('annotations[pagination][order_by]', filters.order_by);
    params.append('annotations[pagination][order]', filters.order || 'desc');
  }

  try {
    const response = await axios.get(
      `https://api-dev.tikram-group.com/api/v1.0/company/trip/index?${params.toString()}`,
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'language': filters.language || 'ar'
        }
      }
    );

    return response.data;
  } catch (error: any) {
    console.error('Error fetching trips:', error);

    // Handle authentication errors
    if (error.response?.status === 401) {
      throw new Error('Authentication failed. Please login again.');
    }

    // Handle other HTTP errors
    if (error.response) {
      throw new Error(`Server error: ${error.response.status} ${error.response.statusText}`);
    }

    // Handle network errors
    if (error.request) {
      throw new Error('Network error. Please check your connection.');
    }

    throw error;
  }
};

export function useFetchTrips(filters: TripFilters = {}) {
  const { i18n } = useTranslation();

  const { data, isLoading, error, refetch } = useQuery({
    queryKey: ['trips', filters, i18n.language],
    queryFn: () => {
      // Update the language header based on current locale
      return fetchTrips({
        ...filters,
        language: i18n.language || 'ar'
      });
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false
  });

  return {
    trips: data?.data?.data || [],
    pagination: data?.data?.pagination,
    isLoading,
    error,
    refetch
  };
}
