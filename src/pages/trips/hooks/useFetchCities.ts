import { useQuery } from '@tanstack/react-query';
import axios from 'axios';
import { getAuthToken } from '../../../lib/cookies';
import { CitiesResponse } from '../lib/types';
import { useTranslation } from 'react-i18next';

export const fetchCities = async (): Promise<CitiesResponse> => {
  const token = getAuthToken();

  if (!token) {
    throw new Error('No authentication token found');
  }

  try {
    const response = await axios.get(
      'https://api-dev.tikram-group.com/api/v1.0/company/city/',
      {
        headers: {
          'Authorization': `Bearer ${token}`,
          'language': 'ar'
        }
      }
    );

    return response.data;
  } catch (error: any) {
    console.error('Error fetching cities:', error);
    
    // Handle authentication errors
    if (error.response?.status === 401) {
      throw new Error('Authentication failed. Please login again.');
    }
    
    // Handle other HTTP errors
    if (error.response) {
      throw new Error(`Server error: ${error.response.status} ${error.response.statusText}`);
    }
    
    // Handle network errors
    if (error.request) {
      throw new Error('Network error. Please check your connection.');
    }
    
    throw error;
  }
};

export function useFetchCities() {
  const { i18n } = useTranslation();

  const { data, isLoading, error } = useQuery({
    queryKey: ['cities', i18n.language],
    queryFn: fetchCities,
    staleTime: 10 * 60 * 1000, // 10 minutes - cities don't change often
    refetchOnWindowFocus: false,
    retry: 1, // Only retry once
    retryOnMount: false,
    refetchOnReconnect: false
  });

  return {
    cities: data?.data?.data || [],
    pagination: data?.data?.pagination,
    isLoading,
    error,
    hasError: !!error
  };
}
