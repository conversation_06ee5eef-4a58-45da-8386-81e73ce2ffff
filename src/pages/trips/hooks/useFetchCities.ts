import { City } from '../lib/types';

// Static city data with Arabic and English translations
const staticCities: City[] = [
  {
    id: 1,
    name: 'Damascus & rural',
    ar_name: 'دمشق وريفها',
    en_name: 'Damascus & rural'
  },
  {
    id: 6,
    name: 'Aleppo',
    ar_name: 'حل<PERSON>',
    en_name: 'Aleppo'
  },
  {
    id: 8,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    ar_name: 'اللاذقية',
    en_name: '<PERSON><PERSON><PERSON><PERSON>'
  },
  {
    id: 17,
    name: 'Qalamoun',
    ar_name: 'القلمون',
    en_name: 'Qalamoun'
  },
  {
    id: 18,
    name: 'Tartous',
    ar_name: 'طرطوس',
    en_name: 'Tartous'
  },
  {
    id: 21,
    name: 'Daraa',
    ar_name: 'درعا',
    en_name: 'Daraa'
  }
];

export function useFetchCities() {
  // Return static data immediately - no API call needed
  return {
    cities: staticCities,
    pagination: {
      total: staticCities.length,
      count: staticCities.length,
      per_page: 10,
      current_page: 1,
      total_pages: 1
    },
    isLoading: false,
    error: null,
    hasError: false
  };
}
