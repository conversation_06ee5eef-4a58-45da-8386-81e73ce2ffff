import DataTable from '@/components/shared/data-table';
import { City } from '../lib/types';
import { columns } from './table/columns';
import { useEffect, useState } from 'react';
import { useFetchCities } from '../hooks/useFetchCities';
import { DataTableSkeleton } from '@/components/shared/data-table-skeleton';
import CityTableActions from './table/city-table-action';

export default function CountriesTable() {
  const [page, setPage] = useState(1);
  const {
    cities,
    pagination,
    isLoading
  }: { cities: City[]; pagination: any; isLoading: boolean } =
    useFetchCities(page);
  useEffect(() => {
    if (!isLoading) {
      setPage(pagination?.page);
    }
  }, [pagination?.page, isLoading]);

  const pageLimit = pagination?.per_page;
  const totalCountries = pagination?.total;
  const pageCount = Math.ceil(totalCountries! / pageLimit);

  return (
    <>
      <CityTableActions />
      {isLoading ? (
        <div className="p-5">
          <DataTableSkeleton columnCount={3} />
        </div>
      ) : (
        <DataTable
          columns={columns}
          data={cities}
          pageCount={pageCount}
          onPageChange={setPage}
        />
      )}
    </>
  );
}
