import Heading from '@/components/shared/heading';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormMessage
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useCreateCountry } from '../../hooks/useCreateCountry';
import { useUpdateCountry } from '../../hooks/useUpdateCountry';
import toast from 'react-hot-toast';
import { Country } from '../../lib/types';

const countryFormSchema = z.object({
  name_en: z
    .string({ required_error: 'English name is required' })
    .min(1, { message: 'Country name should be at least 1 character' }),
  name_ar: z
    .string({ required_error: 'Arabic name is required' })
    .min(1, { message: 'Country name should be at least 1 character' }),
  name_tr: z
    .string({ required_error: 'Turkish name is required' })
    .min(1, { message: 'Country name should be at least 1 character' }),
  currency: z.string().min(2, { message: 'Currency is required' }),
  code: z.string().min(1, { message: 'Code is required' })
});

type CountryFormValue = z.infer<typeof countryFormSchema>;

type CountryFormProps = {
  modalClose: () => void;
  country?: Country;
};

const CountryForm = ({ modalClose, country }: CountryFormProps) => {
  const isEditing = Boolean(country);
  const form = useForm<CountryFormValue>({
    resolver: zodResolver(countryFormSchema),
    defaultValues: country || {
      name_en: '',
      name_ar: '',
      name_tr: '',
      currency: '',
      code: ''
    }
  });

  const { mutate: createCountry, isPending: isCreating } = useCreateCountry();
  const { mutate: updateCountry, isPending: isUpdating } = useUpdateCountry();

  const onSubmit = async (data: CountryFormValue) => {
    if (isEditing && country) {
      updateCountry(
        {
          id: country.id,
          data: {
            name_en: data.name_en,
            name_ar: data.name_ar,
            name_tr: data.name_tr,
            currency: data.currency,
            code: data.code
          }
        },
        {
          onSuccess: () => {
            toast.success('Country updated successfully');
            modalClose();
          },
          onError: (error: any) => console.error(error)
        }
      );
    } else {
      createCountry(data, {
        onSuccess: () => {
          toast.success('New country added successfully');
          modalClose();
        },
        onError: (error: any) => console.error(error)
      });
    }
  };

  return (
    <div className="p-5">
      <Heading
        title={isEditing ? 'Update Country' : 'Create New Country'}
        description={''}
        className="space-y-2 py-4 text-center"
      />
      <Form {...form}>
        <form
          onSubmit={form.handleSubmit(onSubmit)}
          className="space-y-4"
          autoComplete="off"
        >
          <FormField
            control={form.control}
            name="name_en"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    placeholder="name in english"
                    {...field}
                    className="px-4 py-6 shadow-inner drop-shadow-xl"
                    disabled={isCreating || isUpdating}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="name_ar"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    placeholder="name in arabic"
                    {...field}
                    className="px-4 py-6 shadow-inner drop-shadow-xl"
                    disabled={isCreating || isUpdating}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="name_tr"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    placeholder="name in turkish"
                    {...field}
                    className="px-4 py-6 shadow-inner drop-shadow-xl"
                    disabled={isCreating || isUpdating}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="currency"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    placeholder="USD"
                    {...field}
                    className="px-4 py-6 shadow-inner drop-shadow-xl"
                    disabled={isCreating || isUpdating}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            control={form.control}
            name="code"
            render={({ field }) => (
              <FormItem>
                <FormControl>
                  <Input
                    placeholder="Z100"
                    {...field}
                    className="px-4 py-6 shadow-inner drop-shadow-xl"
                    disabled={isCreating || isUpdating}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex items-center justify-center gap-4">
            <Button
              type="button"
              variant="secondary"
              className="rounded-full"
              size="lg"
              onClick={modalClose}
              disabled={isCreating || isUpdating}
            >
              Cancel
            </Button>
            <Button
              disabled={isCreating || isUpdating}
              type="submit"
              className="rounded-full"
              size="lg"
            >
              {isEditing ? 'Update Country' : 'Create Country'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default CountryForm;
