import DataTable from '@/components/shared/data-table';

import CountryTableActions from './table/country-table-action';
import { Country } from '../lib/types';
import { columns } from './table/columns';
import { useFetchCountries } from '../hooks/useFetchCountries';
import { DataTableSkeleton } from '@/components/shared/data-table-skeleton';
import { useEffect, useState } from 'react';

export default function CountriesTable() {
  const [page, setPage] = useState(1);
  const {
    countries,
    pagination,
    isLoading
  }: { countries: Country[]; pagination: any; isLoading: boolean } =
    useFetchCountries(page);

  useEffect(() => {
    if (!isLoading) {
      setPage(pagination?.page);
    }
  }, [pagination?.page, isLoading]);

  const pageLimit = pagination?.per_page;
  const totalCountries = pagination?.total;
  const pageCount = Math.ceil(totalCountries / pageLimit);

  return (
    <>
      <CountryTableActions />
      {isLoading ? (
        <div className="p-5">
          <DataTableSkeleton columnCount={3} />
        </div>
      ) : (
        <DataTable
          columns={columns}
          data={countries}
          pageCount={pageCount}
          onPageChange={setPage}
        />
      )}
    </>
  );
}
