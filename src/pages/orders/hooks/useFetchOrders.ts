import { useQuery } from '@tanstack/react-query';
import { client, setHeaderToken, refreshAuth } from '../../../lib/axiosClient';
import { redirect } from 'react-router-dom';
import toast from 'react-hot-toast';
import { OrderFilters, OrdersListResponse } from '../lib/types';

export const fetchOrders = async (
  page: number,
  filters?: OrderFilters
): Promise<OrdersListResponse> => {
  const token = localStorage.getItem('token');

  // Build query parameters
  const params = new URLSearchParams();
  params.append('page', page.toString());
  params.append('include', 'restaurant');

  // Add filters if provided
  if (filters) {
    if (filters.status) {
      params.append('filter[status]', filters.status);
    }
    if (filters.order_number) {
      params.append('filter[order_number]', filters.order_number);
    }
    // Note: payment_status filter is not supported by the backend API yet
    // The API only accepts 'Status' and 'Order_Number' filters
    // if (filters.payment_status) {
    //   params.append('filter[payment_status]', filters.payment_status);
    // }
  }

  try {
    const response = await client.get(`/admin/orders?${params.toString()}`, {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: any) {
    console.log('error', error);

    if (error.response?.status === 401) {
      const newToken = await refreshAuth();

      if (newToken) {
        setHeaderToken(newToken);
        const retryResponse = await client.get(
          `/admin/orders?${params.toString()}`,
          {
            headers: {
              Authorization: `Bearer ${newToken}`
            }
          }
        );
        return retryResponse.data;
      } else {
        localStorage.removeItem('token');
        toast.error('something went wrong');
        redirect('/login');
        throw new Error('Authentication failed');
      }
    } else {
      throw error;
    }
  }
};

export function useFetchOrders(page: number, filters?: OrderFilters) {
  const { data, isLoading, error } = useQuery({
    queryKey: ['orders', page, filters],
    queryFn: () => fetchOrders(page, filters)
  });

  return {
    orders: data?.data?.items,
    pagination: data?.data?.pagination,
    isLoading,
    error
  };
}
