import { useQuery } from '@tanstack/react-query';
import { client, setHeaderToken, refreshAuth } from '../../../lib/axiosClient';
import { redirect } from 'react-router-dom';
import toast from 'react-hot-toast';

export interface Driver {
  id: number;
  first_name: string;
  last_name: string;
  email: string;
  phone_number: string;
  status: 'ACTIVE' | 'INACTIVE';
  profile_image?: {
    id: number;
    url: string;
    path: string;
  };
  created_at: string;
  updated_at: string;
}

export interface DriversListResponse {
  status: boolean;
  message: string;
  data: {
    items: Driver[];
    pagination?: {
      current_page: number;
      last_page: number;
      from: number;
      to: number;
      per_page: number;
      total: number;
    };
  };
}

export const fetchDrivers = async (): Promise<DriversListResponse> => {
  const token = localStorage.getItem('token');

  try {
    const response = await client.get('/admin/drivers', {
      headers: {
        Authorization: `Bearer ${token}`
      }
    });

    return response.data;
  } catch (error: any) {
    console.log('error', error);

    if (error.response?.status === 401) {
      const newToken = await refreshAuth();

      if (newToken) {
        setHeaderToken(newToken);
        const retryResponse = await client.get('/admin/drivers', {
          headers: {
            Authorization: `Bearer ${newToken}`
          }
        });
        return retryResponse.data;
      } else {
        localStorage.removeItem('token');
        toast.error('something went wrong');
        redirect('/login');
        throw new Error('Authentication failed');
      }
    } else {
      throw error;
    }
  }
};

export function useFetchDrivers() {
  const { data, isLoading, error } = useQuery({
    queryKey: ['drivers'],
    queryFn: () => fetchDrivers()
  });

  return {
    drivers: data?.data?.items || [],
    isLoading,
    error
  };
}
