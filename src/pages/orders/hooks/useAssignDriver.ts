import { useMutation, useQueryClient } from '@tanstack/react-query';
import { client, setHeaderToken, refreshAuth } from '../../../lib/axiosClient';
import { redirect } from 'react-router-dom';
import toast from 'react-hot-toast';

const assignDriver = async ({
  orderId,
  driverId
}: {
  orderId: number;
  driverId: string;
}) => {
  const token = localStorage.getItem('token');

  try {
    const response = await client.post(
      `/admin/orders/${orderId}/assign`,
      {
        driver_id: driverId
      },
      {
        headers: {
          Authorization: `Bearer ${token}`
        }
      }
    );

    return response.data;
  } catch (error: any) {
    console.log('error', error);

    if (error.response?.status === 401) {
      const newToken = await refreshAuth();

      if (newToken) {
        setHeaderToken(newToken);
        const retryResponse = await client.post(
          `/admin/orders/${orderId}/assign`,
          {
            driver_id: driverId
          },
          {
            headers: {
              Authorization: `Bearer ${newToken}`
            }
          }
        );
        return retryResponse.data;
      } else {
        localStorage.removeItem('token');
        toast.error('something went wrong');
        redirect('/login');
        throw new Error('Authentication failed');
      }
    } else {
      throw error;
    }
  }
};

export function useAssignDriver() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: assignDriver,
    onSuccess: (data, variables) => {
      toast.success('Driver assigned successfully!');
      // Invalidate and refetch orders list
      queryClient.invalidateQueries({ queryKey: ['orders'] });
      // Invalidate specific order details
      queryClient.invalidateQueries({
        queryKey: ['order-details', variables.orderId]
      });
    },
    onError: (error: any) => {
      console.error('Error assigning driver:', error);
      const errorMessage =
        error.response?.data?.message || 'Failed to assign driver';
      toast.error(errorMessage);
    }
  });
}
