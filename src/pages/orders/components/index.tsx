import DataTable from '@/components/shared/data-table';
import { OrderFilters } from '../lib/types';
import { columns } from './table/columns';
import { useEffect, useState } from 'react';
import { useFetchOrders } from '../hooks/useFetchOrders';
import { DataTableSkeleton } from '@/components/shared/data-table-skeleton';
import OrderTableActions from './table/order-table-action';

export default function OrdersTable() {
  const [page, setPage] = useState(1);
  const [filters, setFilters] = useState<OrderFilters>({});

  const { orders, pagination, error, isLoading } = useFetchOrders(
    page,
    filters
  );

  useEffect(() => {
    if (!isLoading) {
      setPage(pagination?.current_page || 1);
    }
  }, [pagination?.current_page, isLoading]);

  if (error) {
    console.log(error);
    return (
      <div>
        <h1>Something went wrong</h1>
      </div>
    );
  }

  const pageLimit = pagination?.per_page || 10;
  const totalOrders = pagination?.total || 0;
  const pageCount = Math.ceil(totalOrders / pageLimit);

  const handleFiltersChange = (newFilters: OrderFilters) => {
    setFilters(newFilters);
    setPage(1); // Reset to first page when filters change
  };

  const handleClearFilters = () => {
    setFilters({});
    setPage(1);
  };

  return (
    <>
      <OrderTableActions
        filters={filters}
        onFiltersChange={handleFiltersChange}
        onClearFilters={handleClearFilters}
      />
      {isLoading ? (
        <div className="p-5">
          <DataTableSkeleton columnCount={10} />
        </div>
      ) : (
        <DataTable
          columns={columns}
          data={orders || []}
          pageCount={pageCount}
          onPageChange={setPage}
        />
      )}
    </>
  );
}
