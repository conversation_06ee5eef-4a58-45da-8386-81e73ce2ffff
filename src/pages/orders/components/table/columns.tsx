import { Checkbox } from '@/components/ui/checkbox';
import { ColumnDef } from '@tanstack/react-table';
import { CellAction } from './cell-action';
import { Order } from '../../lib/types';
import { Badge } from '@/components/ui/badge';

const getStatusColor = (status: Order['status']) => {
  switch (status) {
    case 'PENDING':
      return 'bg-yellow-100 text-yellow-800';
    case 'ASSIGNED':
      return 'bg-blue-100 text-blue-800';
    case 'ON_THE_WAY':
      return 'bg-purple-100 text-purple-800';
    case 'DELIVERED':
      return 'bg-green-100 text-green-800';
    case 'ARCHIVED':
      return 'bg-gray-100 text-gray-800';
    case 'CANCELLED':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export const columns: ColumnDef<Order>[] = [
  {
    id: 'select',
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false
  },
  {
    accessorKey: 'id',
    header: 'ID'
  },
  {
    accessorKey: 'order_number',
    header: 'Order Number'
  },
  {
    accessorKey: 'customer_phone',
    header: 'Customer Phone'
  },
  {
    accessorKey: 'restaurant_id',
    header: 'Restaurant ID'
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => (
      <Badge className={getStatusColor(row.original.status)}>
        {row.original.status || 'Unknown'}
      </Badge>
    )
  },
  {
    accessorKey: 'price',
    header: 'Price',
    cell: ({ row }) => {
      const price = row.original.price;
      return price !== undefined && price !== null
        ? `$${price.toFixed(2)}`
        : 'N/A';
    }
  },
  {
    accessorKey: 'delivery_cost',
    header: 'Delivery Cost',
    cell: ({ row }) => {
      const cost = row.original.delivery_cost;
      return cost !== undefined && cost !== null
        ? `$${cost.toFixed(2)}`
        : 'N/A';
    }
  },
  {
    accessorKey: 'total_price',
    header: 'Total Price',
    cell: ({ row }) => {
      const total = row.original.total_price;
      return total !== undefined && total !== null
        ? `$${total.toFixed(2)}`
        : 'N/A';
    }
  },
  {
    accessorKey: 'created_at',
    header: 'Created At',
    cell: ({ row }) => new Date(row.original.created_at).toLocaleDateString()
  },
  {
    id: 'actions',
    cell: ({ row }) => <CellAction data={row.original} />
  }
];
