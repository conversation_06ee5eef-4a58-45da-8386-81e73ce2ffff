import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
  DropdownMenuItem
} from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent } from '@/components/ui/dialog';
import { MoreHorizontal, Eye, UserCheck } from 'lucide-react';
import { useState } from 'react';
import { Order } from '../../lib/types';
import OrderDetailsModal from '../forms/order-details-modal';
import AssignDriverModal from '../forms/assign-driver-modal';

interface CellActionProps {
  data: Order;
}

export const CellAction: React.FC<CellActionProps> = ({ data }) => {
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showAssignDriverModal, setShowAssignDriverModal] = useState(false);

  return (
    <>
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant="ghost" className="h-8 w-8 p-0">
            <span className="sr-only">Open menu</span>
            <MoreHorizontal className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end">
          <DropdownMenuLabel>Actions</DropdownMenuLabel>

          <DropdownMenuItem
            onClick={() => setShowDetailsModal(true)}
            className="cursor-pointer"
          >
            <Eye className="mr-2 h-4 w-4" />
            View Details
          </DropdownMenuItem>

          <DropdownMenuItem
            onClick={() => setShowAssignDriverModal(true)}
            className="cursor-pointer"
          >
            <UserCheck className="mr-2 h-4 w-4" />
            Assign Driver
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>

      {/* Order Details Modal */}
      <Dialog open={showDetailsModal} onOpenChange={setShowDetailsModal}>
        <DialogContent className="max-h-[90vh] max-w-4xl overflow-y-auto">
          <OrderDetailsModal
            modalClose={() => setShowDetailsModal(false)}
            orderId={data.id}
          />
        </DialogContent>
      </Dialog>

      {/* Assign Driver Modal */}
      <Dialog
        open={showAssignDriverModal}
        onOpenChange={setShowAssignDriverModal}
      >
        <DialogContent className="max-w-2xl">
          <AssignDriverModal
            modalClose={() => setShowAssignDriverModal(false)}
            order={data}
          />
        </DialogContent>
      </Dialog>
    </>
  );
};
