import Heading from '@/components/shared/heading';
import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useUpdateOrder } from '../../hooks/useUpdateOrder';
import toast from 'react-hot-toast';
import { Order } from '../../lib/types';

const orderUpdateSchema = z.object({
  status: z.enum([
    'PENDING',
    'ASSIGNED',
    'ON_THE_WAY',
    'DELIVERED',
    'ARCHIVED',
    'CANCELLED'
  ]),
  notes: z.string().optional()
});

type OrderUpdateFormValues = z.infer<typeof orderUpdateSchema>;

interface OrderUpdateModalProps {
  modalClose: () => void;
  order: Order;
}

const OrderUpdateModal = ({ modalClose, order }: OrderUpdateModalProps) => {
  const form = useForm<OrderUpdateFormValues>({
    resolver: zodResolver(orderUpdateSchema),
    defaultValues: {
      status: order.status,
      notes: order.notes || ''
    }
  });

  const { mutate: updateOrder, isPending } = useUpdateOrder();

  const onSubmit = async (data: OrderUpdateFormValues) => {
    updateOrder(
      {
        id: order.id,
        data: {
          status: data.status,
          notes: data.notes
        }
      },
      {
        onSuccess: () => {
          toast.success('Order updated successfully');
          modalClose();
        },
        onError: (error) => {
          toast.error('Failed to update order');
          console.error(error);
        }
      }
    );
  };

  return (
    <div className="space-y-6 p-6">
      <Heading
        title={`Update Order #${order.order_number}`}
        description="Update order status and details"
      />

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Order Status</FormLabel>
                <Select
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select order status" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="PENDING">Pending</SelectItem>
                    <SelectItem value="ASSIGNED">Assigned</SelectItem>
                    <SelectItem value="ON_THE_WAY">On The Way</SelectItem>
                    <SelectItem value="DELIVERED">Delivered</SelectItem>
                    <SelectItem value="ARCHIVED">Archived</SelectItem>
                    <SelectItem value="CANCELLED">Cancelled</SelectItem>
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="notes"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Notes</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Add any notes about this order..."
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="flex justify-end space-x-4">
            <Button type="button" variant="outline" onClick={modalClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isPending}>
              {isPending ? 'Updating...' : 'Update Order'}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default OrderUpdateModal;
