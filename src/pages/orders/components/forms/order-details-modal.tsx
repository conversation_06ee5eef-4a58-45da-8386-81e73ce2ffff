// import Heading from '@/components/shared/heading';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useFetchOrderDetails } from '../../hooks/useFetchOrderDetails';
import { Loader2 } from 'lucide-react';
// import SimpleOrderMap from '@/components/shared/simple-order-map'; // Hidden for now

interface OrderDetailsModalProps {
  modalClose: () => void;
  orderId: number;
}

const OrderDetailsModal = ({ modalClose, orderId }: OrderDetailsModalProps) => {
  const { order, isLoading, error } = useFetchOrderDetails(orderId);

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading order details...</span>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="p-6">
        <div className="text-center text-red-600">
          <p>Error loading order details</p>
          <Button onClick={modalClose} className="mt-4">
            Close
          </Button>
        </div>
      </div>
    );
  }

  // Order not found state
  if (!order) {
    return (
      <div className="p-6">
        <div className="text-center">
          <p>Order not found</p>
          <Button onClick={modalClose} className="mt-4">
            Close
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 bg-[#020817] p-6 text-white">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-700 pb-4">
        <div>
          <h2 className="text-2xl font-bold text-white">
            Order #{order.order_number}
          </h2>
          <p className="mt-1 text-sm text-gray-400">
            Created on {new Date(order.created_at).toLocaleDateString()}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Badge className="bg-blue-600 px-3 py-1 text-sm text-white">
            {order.status}
          </Badge>
          <Button
            variant="outline"
            onClick={modalClose}
            className="border-gray-600 text-white hover:bg-gray-800"
          >
            Close
          </Button>
        </div>
      </div>

      {/* Order Summary Card */}
      <div className="rounded-lg border border-gray-700 bg-gray-800 p-6">
        <h3 className="mb-4 text-lg font-semibold text-white">Order Summary</h3>
        <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
          <div className="text-center">
            <p className="text-sm text-gray-400">Order Price</p>
            <p className="text-xl font-bold text-white">
              {order.price !== undefined && order.price !== null
                ? `$${order.price.toFixed(2)}`
                : 'N/A'}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-400">Delivery Cost</p>
            <p className="text-xl font-bold text-white">
              {order.delivery_cost !== undefined && order.delivery_cost !== null
                ? `$${order.delivery_cost.toFixed(2)}`
                : 'N/A'}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-400">Total Price</p>
            <p className="text-xl font-bold text-green-400">
              {order.total_price !== undefined && order.total_price !== null
                ? `$${order.total_price.toFixed(2)}`
                : 'N/A'}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-400">Status</p>
            <Badge className="mt-1 bg-blue-600 text-white">
              {order.status}
            </Badge>
          </div>
        </div>
      </div>

      {/* Customer & Restaurant Info */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
        {/* Customer Information */}
        <div className="rounded-lg border border-gray-700 bg-gray-800 p-5">
          <h3 className="mb-4 flex items-center text-lg font-semibold text-white">
            <div className="mr-2 h-2 w-2 rounded-full bg-blue-500"></div>
            Customer Information
          </h3>
          <div className="space-y-3">
            <div>
              <p className="text-sm text-gray-400">Phone Number</p>
              <p className="font-medium text-white">{order.customer_phone}</p>
            </div>
          </div>
        </div>

        {/* Restaurant Information */}
        <div className="rounded-lg border border-gray-700 bg-gray-800 p-5">
          <h3 className="mb-4 flex items-center text-lg font-semibold text-white">
            <div className="mr-2 h-2 w-2 rounded-full bg-green-500"></div>
            Restaurant Information
          </h3>
          <div className="space-y-3">
            {order.restaurant || order.resturant ? (
              <>
                <div>
                  <p className="text-sm text-gray-400">Restaurant Name</p>
                  <p className="font-medium text-white">
                    {(order.restaurant || order.resturant)?.name_en}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-400">Phone</p>
                  <p className="font-medium text-white">
                    {(order.restaurant || order.resturant)?.phone}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-400">Address</p>
                  <p className="font-medium text-white">
                    {(order.restaurant || order.resturant)?.address_en}
                  </p>
                </div>
                <div>
                  <p className="text-sm text-gray-400">Status</p>
                  <Badge
                    variant="outline"
                    className="mt-1 border-gray-600 text-white"
                  >
                    {(order.restaurant || order.resturant)?.status}
                  </Badge>
                </div>
              </>
            ) : (
              <div>
                <p className="text-sm text-gray-400">Restaurant ID</p>
                <p className="font-medium text-white">{order.restaurant_id}</p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Additional Information */}
      {(order.distance || order.estimated_time || order.notes) && (
        <div className="rounded-lg border border-gray-700 bg-gray-800 p-5">
          <h3 className="mb-4 flex items-center text-lg font-semibold text-white">
            <div className="mr-2 h-2 w-2 rounded-full bg-orange-500"></div>
            Additional Details
          </h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            {order.distance && (
              <div className="rounded-lg border border-gray-600 bg-gray-700 p-3 text-center">
                <p className="text-sm text-gray-400">Distance</p>
                <p className="text-lg font-semibold text-white">
                  {order.distance} km
                </p>
              </div>
            )}
            {order.estimated_time && (
              <div className="rounded-lg border border-gray-600 bg-gray-700 p-3 text-center">
                <p className="text-sm text-gray-400">Estimated Time</p>
                <p className="text-lg font-semibold text-white">
                  {order.estimated_time}
                </p>
              </div>
            )}
            {order.notes && (
              <div className="rounded-lg border border-gray-600 bg-gray-700 p-3 md:col-span-3">
                <p className="mb-2 text-sm text-gray-400">Order Notes</p>
                <p className="text-white">{order.notes}</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default OrderDetailsModal;
