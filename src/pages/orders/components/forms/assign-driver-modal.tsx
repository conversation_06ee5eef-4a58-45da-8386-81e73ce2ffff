import { Button } from '@/components/ui/button';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage
} from '@/components/ui/form';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from '@/components/ui/select';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { useAssignDriver } from '../../hooks/useAssignDriver';
import { useFetchDrivers, Driver } from '../../hooks/useFetchDrivers';
import { Order } from '../../lib/types';
import { Loader2, UserCheck, User } from 'lucide-react';

const assignDriverSchema = z.object({
  driver_id: z.string().min(1, 'Please select a driver')
});

type AssignDriverFormValues = z.infer<typeof assignDriverSchema>;

// Helper component to display driver info with profile image
const DriverSelectItem = ({ driver }: { driver: Driver }) => {
  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  return (
    <div className="flex items-center space-x-3 py-1">
      <Avatar className="h-8 w-8">
        <AvatarImage
          src={driver.profile_image?.url}
          alt={`${driver.first_name} ${driver.last_name}`}
        />
        <AvatarFallback className="bg-gray-600 text-xs text-white">
          {getInitials(driver.first_name, driver.last_name)}
        </AvatarFallback>
      </Avatar>
      <div className="flex flex-col">
        <span className="font-medium text-white">
          {driver.first_name} {driver.last_name}
        </span>
        <span className="text-sm text-gray-400">{driver.phone_number}</span>
      </div>
    </div>
  );
};

// Helper component for the selected value display
const DriverSelectValue = ({
  driverId,
  drivers
}: {
  driverId: string;
  drivers: Driver[];
}) => {
  const selectedDriver = drivers.find(
    (driver) => String(driver.id) === driverId
  );

  if (!selectedDriver) {
    return <span className="text-gray-400">Choose a driver</span>;
  }

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
  };

  return (
    <div className="flex items-center space-x-2">
      <Avatar className="h-6 w-6">
        <AvatarImage
          src={selectedDriver.profile_image?.url}
          alt={`${selectedDriver.first_name} ${selectedDriver.last_name}`}
        />
        <AvatarFallback className="bg-gray-600 text-xs text-white">
          {getInitials(selectedDriver.first_name, selectedDriver.last_name)}
        </AvatarFallback>
      </Avatar>
      <span className="text-white">
        {selectedDriver.first_name} {selectedDriver.last_name}
      </span>
    </div>
  );
};

interface AssignDriverModalProps {
  modalClose: () => void;
  order: Order;
}

const AssignDriverModal = ({ modalClose, order }: AssignDriverModalProps) => {
  const { drivers, isLoading: driversLoading } = useFetchDrivers();
  const { mutate: assignDriver, isPending: isAssigning } = useAssignDriver();

  const form = useForm<AssignDriverFormValues>({
    resolver: zodResolver(assignDriverSchema),
    defaultValues: {
      driver_id: ''
    }
  });

  const onSubmit = (data: AssignDriverFormValues) => {
    assignDriver(
      {
        orderId: order.id,
        driverId: data.driver_id
      },
      {
        onSuccess: () => {
          modalClose();
        }
      }
    );
  };

  // Filter only active drivers
  const activeDrivers = drivers.filter((driver) => driver.status === 'ACTIVE');

  return (
    <div className="space-y-6 bg-[#020817] p-6 text-white">
      {/* Header */}
      <div className="flex items-center justify-between border-b border-gray-700 pb-4">
        <div className="flex items-center">
          <UserCheck className="mr-3 h-6 w-6 text-blue-500" />
          <div>
            <h2 className="text-xl font-bold text-white">Assign Driver</h2>
            <p className="text-sm text-gray-400">Order #{order.order_number}</p>
          </div>
        </div>
        <Button
          variant="outline"
          onClick={modalClose}
          className="border-gray-600 text-white hover:bg-gray-800"
        >
          Cancel
        </Button>
      </div>

      {/* Form */}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          {/* Driver Selection */}
          <FormField
            control={form.control}
            name="driver_id"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-white">Select Driver</FormLabel>
                <FormControl>
                  {driversLoading ? (
                    <div className="flex items-center justify-center rounded-lg border border-gray-700 bg-gray-800 p-4">
                      <Loader2 className="mr-2 h-5 w-5 animate-spin text-white" />
                      <span className="text-white">Loading drivers...</span>
                    </div>
                  ) : (
                    <Select
                      value={field.value}
                      onValueChange={field.onChange}
                      disabled={isAssigning}
                    >
                      <SelectTrigger className="min-h-[48px] border-gray-700 bg-gray-800 text-white">
                        <SelectValue asChild>
                          <DriverSelectValue
                            driverId={field.value}
                            drivers={activeDrivers}
                          />
                        </SelectValue>
                      </SelectTrigger>
                      <SelectContent className="border-gray-700 bg-gray-800">
                        {activeDrivers.length === 0 ? (
                          <div className="flex items-center justify-center p-4 text-center text-gray-400">
                            <User className="mr-2 h-5 w-5" />
                            No active drivers available
                          </div>
                        ) : (
                          activeDrivers.map((driver) => (
                            <SelectItem
                              key={driver.id}
                              value={String(driver.id)}
                              className="cursor-pointer p-2 text-white hover:bg-gray-700"
                            >
                              <DriverSelectItem driver={driver} />
                            </SelectItem>
                          ))
                        )}
                      </SelectContent>
                    </Select>
                  )}
                </FormControl>
                <FormMessage className="text-red-400" />
              </FormItem>
            )}
          />

          {/* Order Information */}
          <div className="rounded-lg border border-gray-700 bg-gray-800 p-4">
            <h3 className="mb-3 text-sm font-medium text-gray-400">
              Order Information
            </h3>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-gray-400">Order Number:</span>
                <p className="font-medium text-white">{order.order_number}</p>
              </div>
              <div>
                <span className="text-gray-400">Status:</span>
                <p className="font-medium text-white">{order.status}</p>
              </div>
              <div>
                <span className="text-gray-400">Customer Phone:</span>
                <p className="font-medium text-white">{order.customer_phone}</p>
              </div>
              <div>
                <span className="text-gray-400">Total Price:</span>
                <p className="font-medium text-white">
                  {order.total_price !== undefined && order.total_price !== null
                    ? `$${order.total_price.toFixed(2)}`
                    : 'N/A'}
                </p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={modalClose}
              className="flex-1 border-gray-600 text-white hover:bg-gray-800"
              disabled={isAssigning}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="flex-1 bg-blue-600 text-white hover:bg-blue-700"
              disabled={isAssigning || activeDrivers.length === 0}
            >
              {isAssigning ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Assigning...
                </>
              ) : (
                <>
                  <UserCheck className="mr-2 h-4 w-4" />
                  Assign Driver
                </>
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default AssignDriverModal;
