import React, { createContext, useContext, ReactNode } from 'react';
import { useFetchProfile } from '@/hooks/useFetchProfile';

interface EntityPermissions {
  all: boolean;
  show: boolean;
  create: boolean;
  update: boolean;
  delete: boolean;
  change_password?: boolean;
}

interface Permissions {
  profile: EntityPermissions;
  countries: EntityPermissions;
  cities: EntityPermissions;
  areas: EntityPermissions;
  drivers: EntityPermissions;
  orders: EntityPermissions;
}

interface PermissionsContextType {
  permissions: Permissions | null;
  isLoading: boolean;
  error: any;
  hasPermission: (
    entity: keyof Permissions,
    action: keyof EntityPermissions
  ) => boolean;
}

const PermissionsContext = createContext<PermissionsContextType | undefined>(
  undefined
);

interface PermissionsProviderProps {
  children: ReactNode;
}

export const PermissionsProvider: React.FC<PermissionsProviderProps> = ({
  children
}) => {
  const { permissions, isLoading, error } = useFetchProfile();

  const hasPermission = (
    entity: keyof Permissions,
    action: keyof EntityPermissions
  ): boolean => {
    if (!permissions) return false;

    const entityPermissions = permissions[entity];
    if (!entityPermissions) return false;

    // If user has 'all' permission for the entity, they can do everything
    if (entityPermissions.all) return true;

    // Otherwise check the specific action
    return entityPermissions[action] === true;
  };

  const value: PermissionsContextType = {
    permissions,
    isLoading,
    error,
    hasPermission
  };

  return (
    <PermissionsContext.Provider value={value}>
      {children}
    </PermissionsContext.Provider>
  );
};

export const usePermissions = (): PermissionsContextType => {
  const context = useContext(PermissionsContext);
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionsProvider');
  }
  return context;
};
