import { navItems } from '@/constants/data';
import { usePathname } from '@/routes/hooks';
import Heading from './heading';
import UserNav from './user-nav';
import { ModeToggle } from './theme-toggle';
import LanguageSwitcher from './LanguageSwitcher';

// Custom hook to find the matched path
const useMatchedPath = (pathname: string) => {
  const matchedPath =
    navItems.find((item) => item.href === pathname) ||
    navItems.find(
      (item) => pathname.startsWith(item.href + '/') && item.href !== '/'
    );
  return matchedPath?.title || '';
};

export default function Header() {
  const pathname = usePathname();
  const headingText = useMatchedPath(pathname);

  return (
    <div className="flex w-full flex-1 items-center justify-between bg-secondary px-4">
      <Heading title={headingText} />
      <div className=" flex  items-center gap-2 md:ml-6">
        <UserNav />
        <LanguageSwitcher />
        <ModeToggle />
      </div>
    </div>
  );
}
