import { But<PERSON> } from '@/components/ui/button';
import { Modal } from '@/components/ui/modal';
import { Edit, Plus, Eye } from 'lucide-react';
import { useState } from 'react';
import { ScrollArea } from '../ui/scroll-area';
import { useTranslation } from 'react-i18next';

type TPopupModalProps = {
  onConfirm?: () => void;
  loading?: boolean;
  title?: string;
  icon?: string;
  renderModal: (onClose: () => void) => React.ReactNode;
};
export default function PopupModal({
  renderModal,
  title,
  icon
}: TPopupModalProps) {
  const [isOpen, setIsOpen] = useState(false);
  const onClose = () => setIsOpen(false);
  const { t } = useTranslation();
  return (
    <>
      <Button
        className={
          icon == 'Edit' || icon == 'Eye' ? 'w-full p-2' : 'text-xs md:text-sm'
        }
        onClick={() => setIsOpen(true)}
        variant={icon == 'Edit' || icon == 'Eye' ? 'secondary' : 'default'}
      >
        {icon === 'Edit' ? (
          <Edit className="mr-1 w-4" />
        ) : icon === 'Eye' ? (
          <Eye className="mr-1 w-4" />
        ) : (
          <Plus className="mr-2 h-4 w-4" />
        )}
        {title || t('actions.add')}
      </Button>
      <Modal
        isOpen={isOpen}
        onClose={onClose}
        className={'h-[80%] !bg-background !px-1'}
      >
        <ScrollArea className="h-[55%] px-6  ">
          {renderModal(onClose)}
        </ScrollArea>
      </Modal>
    </>
  );
}
