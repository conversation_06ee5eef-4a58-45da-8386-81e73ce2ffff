import React from 'react';

interface SimpleOrderMapProps {
  pickupLatitude: number;
  pickupLongitude: number;
  deliveryLatitude: number;
  deliveryLongitude: number;
  restaurantName?: string;
  customerPhone?: string;
}

const SimpleOrderMap: React.FC<SimpleOrderMapProps> = ({
  pickupLatitude,
  pickupLongitude,
  deliveryLatitude,
  deliveryLongitude,
  restaurantName,
  customerPhone
}) => {
  // Create map URL with both pickup and delivery markers using MapBox
  const createMapUrl = () => {
    // const centerLat = (pickupLatitude + deliveryLatitude) / 2;
    // const centerLng = (pickupLongitude + deliveryLongitude) / 2;

    // // Using MapBox Static API (supports multiple markers without API key for basic usage)
    // const mapboxUrl = `https://api.mapbox.com/styles/v1/mapbox/streets-v11/static/pin-s-restaurant+285A98(${pickupLongitude},${pickupLatitude}),pin-s-circle+DC143C(${deliveryLongitude},${deliveryLatitude})/auto/600x300?access_token=pk.eyJ1IjoibWFwYm94IiwiYSI6ImNpejY4NXVycTA2emYycXBndHRqcmZ3N3gifQ.rJcFIG214AriISLbB6B5aw`;

    // // Fallback to a simple iframe map using HERE Maps
    // const hereMapUrl = `https://wego.here.com/directions/mix/${pickupLatitude},${pickupLongitude}/${deliveryLatitude},${deliveryLongitude}?map=${centerLat},${centerLng},13,normal&fb_locale=en_US`;

    // // Using Leaflet with OpenStreetMap tiles (most reliable)
    // const leafletUrl = `https://maps.locationiq.com/v3/staticmap?key=pk.locationiq.com&center=${centerLat},${centerLng}&zoom=12&size=600x300&format=png&markers=icon:small-red-cutout|${pickupLatitude},${pickupLongitude}||icon:small-blue-cutout|${deliveryLatitude},${deliveryLongitude}`;

    // // Simple Google Maps with directions (most reliable for two points)
    // const googleDirectionsUrl = `https://www.google.com/maps/embed/v1/directions?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dOWTgHz-EuIlmA&origin=${pickupLatitude},${pickupLongitude}&destination=${deliveryLatitude},${deliveryLongitude}&mode=driving`;

    // Fallback to simple Google Maps view
    return `https://maps.google.com/maps?q=${pickupLatitude},${pickupLongitude}&q=${deliveryLatitude},${deliveryLongitude}&z=13&output=embed`;
  };

  return (
    <div className="w-full overflow-hidden rounded-lg border bg-white shadow-sm">
      {/* Simple Map */}
      <iframe
        src={createMapUrl()}
        width="100%"
        height="300"
        style={{ border: 0 }}
        loading="lazy"
        title="Order Map - Pickup and Delivery Locations"
        className="w-full"
      />

      {/* Simple Legend */}
      <div className="border-t bg-gray-50 p-3">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-4">
            <div className="flex items-center">
              <div className="mr-2 h-3 w-3 rounded-full bg-green-500"></div>
              <span className="text-gray-700">Pickup</span>
              {restaurantName && (
                <span className="ml-1 text-gray-500">({restaurantName})</span>
              )}
            </div>
            <div className="flex items-center">
              <div className="mr-2 h-3 w-3 rounded-full bg-red-500"></div>
              <span className="text-gray-700">Delivery</span>
              {customerPhone && (
                <span className="ml-1 text-gray-500">({customerPhone})</span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleOrderMap;
