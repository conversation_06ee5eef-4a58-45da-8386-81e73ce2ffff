<picture>
  <source media="(prefers-color-scheme: dark)" srcset="https://user-images.githubusercontent.com/9113740/201498864-2a900c64-d88f-4ed4-b5cf-770bcb57e1f5.png">
  <source media="(prefers-color-scheme: light)" srcset="https://user-images.githubusercontent.com/9113740/201498152-b171abb8-9225-487a-821c-6ff49ee48579.png">
</picture>

<div align="center"><strong>React Admin Dashboard Starter Template With Shadcn-ui</strong></div>
<div align="center">Built with the Vite + React Ts</div>
<br />
<div align="center">
<a href="https://react-shadcn-dashboard-starter.vercel.app/">View Demo</a>
<span>
</div>

## Overview

This is a starter template using the following stack:

- Js Library - [React 18](https://react.dev/)
- Language - [TypeScript](https://www.typescriptlang.org)
- Styling - [Tailwind CSS](https://tailwindcss.com)
- Components - [Shadcn-ui](https://ui.shadcn.com)
- Schema Validations - [Zod](https://zod.dev)
- Async state management - [Tanstack Query aka React Query](https://tanstack.com/query/latest/docs/framework/react/overview)
- Tables - [Tanstack Tables](https://ui.shadcn.com/docs/components/data-table)
- Forms - [React Hook Form](https://ui.shadcn.com/docs/components/form)
- Linting - [ESLint](https://eslint.org)
- Formatting - [Prettier](https://prettier.io)
- Pre-commit hook - [Husky](https://typicode.github.io/husky/)

## Pages

| Pages                                                                  | Specifications                                                                   |
| :--------------------------------------------------------------------- | :------------------------------------------------------------------------------- |
| [Signup](https://react-shadcn-dashboard-starter.vercel.app/login)      | Custom auth.                                                                     |
| [Dashboard](https://react-shadcn-dashboard-starter.vercel.app/)        | Cards with recharts graphs for analytics with dark mode ✅.                      |
| [Students](https://react-shadcn-dashboard-starter.vercel.app/students) | Tanstack tables with students details with server side searching, pagination etc |
| [404](https://react-shadcn-dashboard-starter.vercel.app/404)           | Not Found Page                                                                   |
| -                                                                      | -                                                                                |

## Getting Started

Follow these steps to clone the repository and start the development server:

- `git clone https://github.com/Kiranism/react-shadcn-dashboard-starter.git`
- `npm install`
- `npm run dev`

You should now be able to access the application at http://localhost:5173.
